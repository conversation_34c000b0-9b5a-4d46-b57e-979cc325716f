const { Sequelize, DataTypes } = require('sequelize');

// Setup MySQL connection
const sequelize = new Sequelize(
  process.env.DB_NAME,
  process.env.DB_USER,
  process.env.DB_PASSWORD,
  {
    host: process.env.DB_HOST,
    dialect: 'mysql',
  }
);

// User model
const User = sequelize.define('User', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  full_name: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  email: {
    type: DataTypes.STRING(255),
    unique: true,
    allowNull: false,
    validate: {
      isEmail: true,
    },
  },
  phone: {
    type: DataTypes.STRING(40),
    allowNull: true,
  },
  password_hash: {
    type: DataTypes.STRING(255),
    allowNull: true, // Can be null for OAuth-only users
  },
  linkedin_url: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  locale: {
    type: DataTypes.STRING(8),
    defaultValue: 'en',
  },
  is_premium: {
    type: DataTypes.TINYINT,
    defaultValue: 0,
  },
}, {
  timestamps: true,
  createdAt: 'created_at',
  updatedAt: false,
  tableName: 'users',
});

module.exports = { sequelize, User };
