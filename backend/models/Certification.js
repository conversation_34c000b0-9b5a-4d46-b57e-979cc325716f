const { DataTypes } = require('sequelize');
const { sequelize } = require('./User');

const Certification = sequelize.define('Certification', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  resume_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'resumes',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  name: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  issuing_organization: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  issue_date: {
    type: DataTypes.DATEONLY,
    allowNull: false,
  },
  expiration_date: {
    type: DataTypes.DATEONLY,
    allowNull: true, // null means no expiration
  },
  credential_id: {
    type: DataTypes.STRING(255),
    allowNull: true,
  },
  credential_url: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  sort_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'certifications',
  timestamps: false,
});

module.exports = Certification;
