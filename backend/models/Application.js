const { DataTypes } = require('sequelize');
const { sequelize } = require('./User');

const Application = sequelize.define('Application', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  resume_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'resumes',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  company_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'companies',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  job_title: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  job_description: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  cover_letter: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  status: {
    type: DataTypes.ENUM('pending', 'submitted', 'reviewed', 'interview', 'rejected', 'accepted'),
    allowNull: false,
    defaultValue: 'pending'
  },
  application_url: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  salary_range: {
    type: DataTypes.STRING(100),
    allowNull: true,
  },
  notes: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  applied_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'applications',
  timestamps: false,
});

module.exports = Application;
