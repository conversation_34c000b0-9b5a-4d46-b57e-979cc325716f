const { DataTypes } = require('sequelize');
const { sequelize } = require('./User');

const Resume = sequelize.define('Resume', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  user_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'users',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  title: {
    type: DataTypes.STRING(255),
    allowNull: false,
  },
  objective: {
    type: DataTypes.TEXT,
    allowNull: true,
  },
  template_key: {
    type: DataTypes.STRING(40),
    allowNull: false,
    defaultValue: 'classic'
  },
  language: {
    type: DataTypes.STRING(8),
    allowNull: false,
    defaultValue: 'en'
  },
  visibility: {
    type: DataTypes.ENUM('private', 'public', 'market'),
    allowNull: false,
    defaultValue: 'private'
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  },
  updated_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'resumes',
  timestamps: false, // We're handling timestamps manually
  hooks: {
    beforeUpdate: (resume) => {
      resume.updated_at = new Date();
    }
  }
});

module.exports = Resume;
