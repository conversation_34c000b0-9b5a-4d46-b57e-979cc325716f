const { DataTypes } = require('sequelize');
const { sequelize } = require('./User');

const Language = sequelize.define('Language', {
  id: {
    type: DataTypes.INTEGER,
    autoIncrement: true,
    primaryKey: true,
  },
  resume_id: {
    type: DataTypes.INTEGER,
    allowNull: false,
    references: {
      model: 'resumes',
      key: 'id'
    },
    onDelete: 'CASCADE'
  },
  name: {
    type: DataTypes.STRING(100),
    allowNull: false,
  },
  proficiency_level: {
    type: DataTypes.ENUM('basic', 'conversational', 'fluent', 'native'),
    allowNull: false,
  },
  can_read: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  can_write: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  can_speak: {
    type: DataTypes.BOOLEAN,
    allowNull: false,
    defaultValue: true
  },
  sort_order: {
    type: DataTypes.INTEGER,
    allowNull: false,
    defaultValue: 0
  },
  created_at: {
    type: DataTypes.DATE,
    allowNull: false,
    defaultValue: DataTypes.NOW
  }
}, {
  tableName: 'languages',
  timestamps: false,
});

module.exports = Language;
