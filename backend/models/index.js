const { sequelize, User } = require('./User');
const Resume = require('./Resume');
const Experience = require('./Experience');
const Education = require('./Education');
const Skill = require('./Skill');
const Certification = require('./Certification');
const Language = require('./Language');
const Company = require('./Company');
const Application = require('./Application');

// Define associations
// User -> Resume (One to Many)
User.hasMany(Resume, { foreignKey: 'user_id', as: 'resumes' });
Resume.belongsTo(User, { foreignKey: 'user_id', as: 'user' });

// Resume -> Experience (One to Many)
Resume.hasMany(Experience, { foreignKey: 'resume_id', as: 'experiences' });
Experience.belongsTo(Resume, { foreignKey: 'resume_id', as: 'resume' });

// Resume -> Education (One to Many)
Resume.hasMany(Education, { foreignKey: 'resume_id', as: 'educations' });
Education.belongsTo(Resume, { foreignKey: 'resume_id', as: 'resume' });

// Resume -> Skill (One to Many)
Resume.hasMany(Skill, { foreignKey: 'resume_id', as: 'skills' });
Skill.belongsTo(Resume, { foreignKey: 'resume_id', as: 'resume' });

// Resume -> Certification (One to Many)
Resume.hasMany(Certification, { foreignKey: 'resume_id', as: 'certifications' });
Certification.belongsTo(Resume, { foreignKey: 'resume_id', as: 'resume' });

// Resume -> Language (One to Many)
Resume.hasMany(Language, { foreignKey: 'resume_id', as: 'languages' });
Language.belongsTo(Resume, { foreignKey: 'resume_id', as: 'resume' });

// Resume -> Application (One to Many)
Resume.hasMany(Application, { foreignKey: 'resume_id', as: 'applications' });
Application.belongsTo(Resume, { foreignKey: 'resume_id', as: 'resume' });

// Company -> Application (One to Many)
Company.hasMany(Application, { foreignKey: 'company_id', as: 'applications' });
Application.belongsTo(Company, { foreignKey: 'company_id', as: 'company' });

// Export all models and sequelize instance
module.exports = {
  sequelize,
  User,
  Resume,
  Experience,
  Education,
  Skill,
  Certification,
  Language,
  Company,
  Application
};
