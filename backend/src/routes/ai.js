const express = require('express')
const router = express.Router()
const aiController = require('../controllers/aiController')
const auth = require('../middleware/auth')
const rateLimit = require('express-rate-limit')

// Rate limiting for AI endpoints (more restrictive)
const aiRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // Limit each IP to 10 AI requests per windowMs
  message: {
    success: false,
    message: 'Too many AI requests, please try again later'
  },
  standardHeaders: true,
  legacyHeaders: false
})

// Premium rate limiting (higher limits for premium users)
const premiumRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Higher limit for premium users
  message: {
    success: false,
    message: 'Rate limit exceeded, please upgrade to premium for higher limits'
  }
})

// Middleware to check if user has AI credits/permissions
const checkAIPermissions = (req, res, next) => {
  // In a real implementation, check user's AI usage limits
  // For now, allow all authenticated users
  next()
}

// Apply authentication and rate limiting to all AI routes
router.use(auth)
router.use(aiRateLimit)
router.use(checkAIPermissions)

/**
 * @route   POST /api/ai/enhance-summary
 * @desc    Enhance professional summary using AI
 * @access  Private
 */
router.post('/enhance-summary', aiController.enhanceSummary)

/**
 * @route   POST /api/ai/enhance-job-description
 * @desc    Enhance job description using AI
 * @access  Private
 */
router.post('/enhance-job-description', aiController.enhanceJobDescription)

/**
 * @route   POST /api/ai/suggest-skills
 * @desc    Get skill suggestions based on job title and industry
 * @access  Private
 */
router.post('/suggest-skills', aiController.suggestSkills)

/**
 * @route   POST /api/ai/optimize-ats
 * @desc    Get ATS optimization recommendations
 * @access  Private
 */
router.post('/optimize-ats', aiController.optimizeForATS)

/**
 * @route   POST /api/ai/generate-achievements
 * @desc    Generate achievement suggestions
 * @access  Private
 */
router.post('/generate-achievements', aiController.generateAchievements)

/**
 * @route   POST /api/ai/generate-cover-letter
 * @desc    Generate cover letter based on resume and job description
 * @access  Private
 */
router.post('/generate-cover-letter', aiController.generateCoverLetter)

/**
 * @route   POST /api/ai/generate-interview-questions
 * @desc    Generate interview questions based on resume and target role
 * @access  Private
 */
router.post('/generate-interview-questions', aiController.generateInterviewQuestions)

/**
 * @route   GET /api/ai/usage-stats
 * @desc    Get AI usage statistics for the current user
 * @access  Private
 */
router.get('/usage-stats', aiController.getUsageStats)

module.exports = router
