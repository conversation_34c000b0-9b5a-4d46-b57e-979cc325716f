const aiService = require('../services/aiService')
const { Resume, Experience, Education, Skill } = require('../models')

class AIController {
  // Enhance professional summary
  async enhanceSummary(req, res) {
    try {
      const { summary, jobTitle, industry, experience } = req.body

      if (!summary) {
        return res.status(400).json({
          success: false,
          message: 'Summary text is required'
        })
      }

      const enhancedSummary = await aiService.enhanceSummary(
        summary,
        jobTitle || 'Professional',
        industry || 'Technology',
        experience || '3-5 years'
      )

      res.json({
        success: true,
        data: {
          original: summary,
          enhanced: enhancedSummary
        }
      })
    } catch (error) {
      console.error('Error enhancing summary:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to enhance summary'
      })
    }
  }

  // Enhance job description
  async enhanceJobDescription(req, res) {
    try {
      const { description, jobTitle, company, achievements } = req.body

      if (!description) {
        return res.status(400).json({
          success: false,
          message: 'Job description is required'
        })
      }

      const enhancedDescription = await aiService.enhanceJobDescription(
        description,
        jobTitle || 'Professional',
        company || 'Company',
        achievements || []
      )

      res.json({
        success: true,
        data: {
          original: description,
          enhanced: enhancedDescription
        }
      })
    } catch (error) {
      console.error('Error enhancing job description:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to enhance job description'
      })
    }
  }

  // Suggest skills based on job title and industry
  async suggestSkills(req, res) {
    try {
      const { jobTitle, industry, currentSkills } = req.body

      if (!jobTitle) {
        return res.status(400).json({
          success: false,
          message: 'Job title is required'
        })
      }

      const suggestedSkills = await aiService.suggestSkills(
        jobTitle,
        industry || 'Technology',
        currentSkills || []
      )

      res.json({
        success: true,
        data: {
          suggestedSkills,
          count: suggestedSkills.length
        }
      })
    } catch (error) {
      console.error('Error suggesting skills:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to suggest skills'
      })
    }
  }

  // Optimize resume for ATS
  async optimizeForATS(req, res) {
    try {
      const { resumeId, targetJobTitle, targetIndustry } = req.body
      const userId = req.user.id

      if (!resumeId) {
        return res.status(400).json({
          success: false,
          message: 'Resume ID is required'
        })
      }

      // Get resume data
      const resume = await Resume.findOne({
        where: { id: resumeId, userId },
        include: [
          { model: Experience },
          { model: Education },
          { model: Skill }
        ]
      })

      if (!resume) {
        return res.status(404).json({
          success: false,
          message: 'Resume not found'
        })
      }

      const recommendations = await aiService.optimizeForATS(
        resume,
        targetJobTitle || 'Professional',
        targetIndustry || 'Technology'
      )

      res.json({
        success: true,
        data: {
          recommendations,
          resumeId,
          targetJobTitle,
          targetIndustry
        }
      })
    } catch (error) {
      console.error('Error optimizing for ATS:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to optimize resume for ATS'
      })
    }
  }

  // Generate achievement suggestions
  async generateAchievements(req, res) {
    try {
      const { jobTitle, industry, responsibilities } = req.body

      if (!jobTitle || !responsibilities) {
        return res.status(400).json({
          success: false,
          message: 'Job title and responsibilities are required'
        })
      }

      const achievements = await aiService.generateAchievements(
        jobTitle,
        industry || 'Technology',
        responsibilities
      )

      res.json({
        success: true,
        data: {
          achievements,
          count: achievements.length
        }
      })
    } catch (error) {
      console.error('Error generating achievements:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to generate achievements'
      })
    }
  }

  // Generate cover letter
  async generateCoverLetter(req, res) {
    try {
      const { resumeId, jobDescription, companyName } = req.body
      const userId = req.user.id

      if (!resumeId || !jobDescription || !companyName) {
        return res.status(400).json({
          success: false,
          message: 'Resume ID, job description, and company name are required'
        })
      }

      // Get resume data
      const resume = await Resume.findOne({
        where: { id: resumeId, userId },
        include: [
          { model: Experience },
          { model: Education },
          { model: Skill }
        ]
      })

      if (!resume) {
        return res.status(404).json({
          success: false,
          message: 'Resume not found'
        })
      }

      const coverLetter = await aiService.generateCoverLetter(
        resume,
        jobDescription,
        companyName
      )

      res.json({
        success: true,
        data: {
          coverLetter,
          companyName,
          resumeId
        }
      })
    } catch (error) {
      console.error('Error generating cover letter:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to generate cover letter'
      })
    }
  }

  // Generate interview questions
  async generateInterviewQuestions(req, res) {
    try {
      const { resumeId, jobTitle, industry } = req.body
      const userId = req.user.id

      if (!resumeId) {
        return res.status(400).json({
          success: false,
          message: 'Resume ID is required'
        })
      }

      // Get resume data
      const resume = await Resume.findOne({
        where: { id: resumeId, userId },
        include: [
          { model: Experience },
          { model: Education },
          { model: Skill }
        ]
      })

      if (!resume) {
        return res.status(404).json({
          success: false,
          message: 'Resume not found'
        })
      }

      const questions = await aiService.generateInterviewQuestions(
        jobTitle || 'Professional',
        industry || 'Technology',
        resume
      )

      res.json({
        success: true,
        data: {
          questions,
          count: questions.length,
          jobTitle,
          industry
        }
      })
    } catch (error) {
      console.error('Error generating interview questions:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to generate interview questions'
      })
    }
  }

  // Get AI usage statistics for user
  async getUsageStats(req, res) {
    try {
      const userId = req.user.id

      // In a real implementation, you would track AI usage in the database
      // For now, return mock data
      const stats = {
        totalRequests: 25,
        summaryEnhancements: 8,
        jobDescriptionEnhancements: 12,
        skillSuggestions: 5,
        monthlyLimit: 100,
        remainingRequests: 75,
        isPremium: false
      }

      res.json({
        success: true,
        data: stats
      })
    } catch (error) {
      console.error('Error getting usage stats:', error)
      res.status(500).json({
        success: false,
        message: 'Failed to get usage statistics'
      })
    }
  }
}

module.exports = new AIController()
