const { Resume, Experience, Education, Skill, Certification, Language, User } = require('../models');

// Get all resumes for authenticated user
const getUserResumes = async (req, res) => {
  try {
    const resumes = await Resume.findAll({
      where: { user_id: req.user.id },
      include: [
        { model: Experience, as: 'experiences' },
        { model: Education, as: 'educations' },
        { model: Skill, as: 'skills' },
        { model: Certification, as: 'certifications' },
        { model: Language, as: 'languages' }
      ],
      order: [['updated_at', 'DESC']]
    });

    res.json({ resumes });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Get single resume by ID
const getResume = async (req, res) => {
  try {
    const { id } = req.params;
    
    const resume = await Resume.findOne({
      where: { 
        id, 
        user_id: req.user.id 
      },
      include: [
        { model: Experience, as: 'experiences', order: [['sort_order', 'ASC']] },
        { model: Education, as: 'educations', order: [['sort_order', 'ASC']] },
        { model: Skill, as: 'skills', order: [['sort_order', 'ASC']] },
        { model: Certification, as: 'certifications', order: [['sort_order', 'ASC']] },
        { model: Language, as: 'languages', order: [['sort_order', 'ASC']] }
      ]
    });

    if (!resume) {
      return res.status(404).json({ error: 'Resume not found' });
    }

    res.json({ resume });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Create new resume
const createResume = async (req, res) => {
  try {
    const { title, objective, template_key, language, visibility } = req.body;

    if (!title) {
      return res.status(400).json({ error: 'Resume title is required' });
    }

    const resume = await Resume.create({
      user_id: req.user.id,
      title,
      objective: objective || '',
      template_key: template_key || 'classic',
      language: language || 'en',
      visibility: visibility || 'private'
    });

    res.status(201).json({ 
      message: 'Resume created successfully',
      resume 
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Update resume
const updateResume = async (req, res) => {
  try {
    const { id } = req.params;
    const { title, objective, template_key, language, visibility } = req.body;

    const resume = await Resume.findOne({
      where: { 
        id, 
        user_id: req.user.id 
      }
    });

    if (!resume) {
      return res.status(404).json({ error: 'Resume not found' });
    }

    await resume.update({
      title: title || resume.title,
      objective: objective !== undefined ? objective : resume.objective,
      template_key: template_key || resume.template_key,
      language: language || resume.language,
      visibility: visibility || resume.visibility,
      updated_at: new Date()
    });

    res.json({ 
      message: 'Resume updated successfully',
      resume 
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Delete resume
const deleteResume = async (req, res) => {
  try {
    const { id } = req.params;

    const resume = await Resume.findOne({
      where: { 
        id, 
        user_id: req.user.id 
      }
    });

    if (!resume) {
      return res.status(404).json({ error: 'Resume not found' });
    }

    await resume.destroy();

    res.json({ message: 'Resume deleted successfully' });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

// Duplicate resume
const duplicateResume = async (req, res) => {
  try {
    const { id } = req.params;

    const originalResume = await Resume.findOne({
      where: { 
        id, 
        user_id: req.user.id 
      },
      include: [
        { model: Experience, as: 'experiences' },
        { model: Education, as: 'educations' },
        { model: Skill, as: 'skills' },
        { model: Certification, as: 'certifications' },
        { model: Language, as: 'languages' }
      ]
    });

    if (!originalResume) {
      return res.status(404).json({ error: 'Resume not found' });
    }

    // Create new resume
    const newResume = await Resume.create({
      user_id: req.user.id,
      title: `${originalResume.title} (Copy)`,
      objective: originalResume.objective,
      template_key: originalResume.template_key,
      language: originalResume.language,
      visibility: 'private'
    });

    // Copy all related data
    if (originalResume.experiences?.length > 0) {
      const experiences = originalResume.experiences.map(exp => ({
        resume_id: newResume.id,
        company_name: exp.company_name,
        position: exp.position,
        location: exp.location,
        start_date: exp.start_date,
        end_date: exp.end_date,
        is_current: exp.is_current,
        description: exp.description,
        achievements: exp.achievements,
        sort_order: exp.sort_order
      }));
      await Experience.bulkCreate(experiences);
    }

    // Copy other sections similarly...
    // (Education, Skills, Certifications, Languages)

    res.status(201).json({ 
      message: 'Resume duplicated successfully',
      resume: newResume 
    });
  } catch (error) {
    res.status(500).json({ error: error.message });
  }
};

module.exports = {
  getUserResumes,
  getResume,
  createResume,
  updateResume,
  deleteResume,
  duplicateResume
};
