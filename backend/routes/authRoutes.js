const express = require('express');
const router = express.Router();
const { register, login, refresh, me, logout } = require('../controllers/authController');
const { authenticateToken } = require('../middleware/auth');

// Register route
router.post('/register', register);

// Login route
router.post('/login', login);

// Refresh token route
router.post('/refresh', refresh);

// Get current user (protected)
router.get('/me', authenticateToken, me);

// Logout route (protected)
router.post('/logout', authenticateToken, logout);

module.exports = router;
