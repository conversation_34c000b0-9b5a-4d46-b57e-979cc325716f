// CV Templates Data
export const templateCategories = [
  { id: 'professional', name: 'Professional', description: 'Clean and professional designs' },
  { id: 'creative', name: 'Creative', description: 'Modern and creative layouts' },
  { id: 'minimal', name: 'Minimal', description: 'Simple and elegant designs' },
  { id: 'executive', name: 'Executive', description: 'Premium executive templates' }
]

export const cvTemplates = [
  {
    id: 'modern-professional',
    name: 'Modern Professional',
    category: 'professional',
    description: 'A clean, professional template perfect for corporate roles',
    preview: '/templates/modern-professional-preview.jpg',
    isPremium: false,
    features: ['ATS-Friendly', 'Clean Layout', 'Professional'],
    colors: {
      primary: '#2563eb',
      secondary: '#64748b',
      accent: '#0f172a'
    }
  },
  {
    id: 'creative-designer',
    name: 'Creative Designer',
    category: 'creative',
    description: 'Perfect for designers and creative professionals',
    preview: '/templates/creative-designer-preview.jpg',
    isPremium: false,
    features: ['Creative Layout', 'Portfolio Section', 'Modern Design'],
    colors: {
      primary: '#7c3aed',
      secondary: '#a855f7',
      accent: '#1e1b4b'
    }
  },
  {
    id: 'minimal-clean',
    name: 'Minimal Clean',
    category: 'minimal',
    description: 'Simple and elegant design that focuses on content',
    preview: '/templates/minimal-clean-preview.jpg',
    isPremium: false,
    features: ['Minimal Design', 'Easy to Read', 'ATS-Optimized'],
    colors: {
      primary: '#374151',
      secondary: '#6b7280',
      accent: '#111827'
    }
  },
  {
    id: 'executive-premium',
    name: 'Executive Premium',
    category: 'executive',
    description: 'Premium template for senior executives and managers',
    preview: '/templates/executive-premium-preview.jpg',
    isPremium: true,
    features: ['Premium Design', 'Executive Layout', 'Professional'],
    colors: {
      primary: '#059669',
      secondary: '#10b981',
      accent: '#064e3b'
    }
  },
  {
    id: 'tech-developer',
    name: 'Tech Developer',
    category: 'professional',
    description: 'Optimized for software developers and tech professionals',
    preview: '/templates/tech-developer-preview.jpg',
    isPremium: false,
    features: ['Tech-Focused', 'Skills Highlight', 'Modern'],
    colors: {
      primary: '#0891b2',
      secondary: '#06b6d4',
      accent: '#164e63'
    }
  },
  {
    id: 'marketing-specialist',
    name: 'Marketing Specialist',
    category: 'creative',
    description: 'Perfect for marketing and sales professionals',
    preview: '/templates/marketing-specialist-preview.jpg',
    isPremium: true,
    features: ['Marketing Focus', 'Results Oriented', 'Eye-catching'],
    colors: {
      primary: '#dc2626',
      secondary: '#ef4444',
      accent: '#7f1d1d'
    }
  }
]

// Helper functions
export const getTemplatesByCategory = (categoryId) => {
  if (!categoryId) return cvTemplates
  return cvTemplates.filter(template => template.category === categoryId)
}

export const getTemplateById = (templateId) => {
  return cvTemplates.find(template => template.id === templateId)
}

export const getFreeTemplates = () => {
  return cvTemplates.filter(template => !template.isPremium)
}

export const getPremiumTemplates = () => {
  return cvTemplates.filter(template => template.isPremium)
}

export const getPopularTemplates = () => {
  // Return first 3 templates as popular
  return cvTemplates.slice(0, 3)
}

export const searchTemplates = (query) => {
  const lowercaseQuery = query.toLowerCase()
  return cvTemplates.filter(template => 
    template.name.toLowerCase().includes(lowercaseQuery) ||
    template.description.toLowerCase().includes(lowercaseQuery) ||
    template.features.some(feature => feature.toLowerCase().includes(lowercaseQuery))
  )
}
