const API_BASE_URL = process.env.REACT_APP_API_URL || 'http://localhost:3001/api'

class AIService {
  constructor() {
    this.baseURL = `${API_BASE_URL}/ai`
  }

  // Get auth token from localStorage
  getAuthToken() {
    return localStorage.getItem('token')
  }

  // Make authenticated API request
  async makeRequest(endpoint, options = {}) {
    const token = this.getAuthToken()
    
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...(token && { Authorization: `Bearer ${token}` }),
        ...options.headers
      },
      ...options
    }

    const response = await fetch(`${this.baseURL}${endpoint}`, config)
    const data = await response.json()

    if (!response.ok) {
      throw new Error(data.message || 'AI service request failed')
    }

    return data
  }

  // Enhance professional summary
  async enhanceSummary(summary, jobTitle = '', industry = '', experience = '') {
    try {
      const response = await this.makeRequest('/enhance-summary', {
        method: 'POST',
        body: JSON.stringify({
          summary,
          jobTitle,
          industry,
          experience
        })
      })

      return response.data.enhanced
    } catch (error) {
      console.error('Error enhancing summary:', error)
      throw error
    }
  }

  // Enhance job description
  async enhanceJobDescription(description, jobTitle = '', company = '', achievements = []) {
    try {
      const response = await this.makeRequest('/enhance-job-description', {
        method: 'POST',
        body: JSON.stringify({
          description,
          jobTitle,
          company,
          achievements
        })
      })

      return response.data.enhanced
    } catch (error) {
      console.error('Error enhancing job description:', error)
      throw error
    }
  }

  // Get skill suggestions
  async suggestSkills(jobTitle, industry = '', currentSkills = []) {
    try {
      const response = await this.makeRequest('/suggest-skills', {
        method: 'POST',
        body: JSON.stringify({
          jobTitle,
          industry,
          currentSkills: currentSkills.map(skill => typeof skill === 'string' ? skill : skill.name)
        })
      })

      return response.data.suggestedSkills
    } catch (error) {
      console.error('Error suggesting skills:', error)
      throw error
    }
  }

  // Get ATS optimization recommendations
  async optimizeForATS(resumeId, targetJobTitle = '', targetIndustry = '') {
    try {
      const response = await this.makeRequest('/optimize-ats', {
        method: 'POST',
        body: JSON.stringify({
          resumeId,
          targetJobTitle,
          targetIndustry
        })
      })

      return response.data.recommendations
    } catch (error) {
      console.error('Error optimizing for ATS:', error)
      throw error
    }
  }

  // Generate achievement suggestions
  async generateAchievements(jobTitle, industry = '', responsibilities = '') {
    try {
      const response = await this.makeRequest('/generate-achievements', {
        method: 'POST',
        body: JSON.stringify({
          jobTitle,
          industry,
          responsibilities
        })
      })

      return response.data.achievements
    } catch (error) {
      console.error('Error generating achievements:', error)
      throw error
    }
  }

  // Generate cover letter
  async generateCoverLetter(resumeId, jobDescription, companyName) {
    try {
      const response = await this.makeRequest('/generate-cover-letter', {
        method: 'POST',
        body: JSON.stringify({
          resumeId,
          jobDescription,
          companyName
        })
      })

      return response.data.coverLetter
    } catch (error) {
      console.error('Error generating cover letter:', error)
      throw error
    }
  }

  // Generate interview questions
  async generateInterviewQuestions(resumeId, jobTitle = '', industry = '') {
    try {
      const response = await this.makeRequest('/generate-interview-questions', {
        method: 'POST',
        body: JSON.stringify({
          resumeId,
          jobTitle,
          industry
        })
      })

      return response.data.questions
    } catch (error) {
      console.error('Error generating interview questions:', error)
      throw error
    }
  }

  // Get AI usage statistics
  async getUsageStats() {
    try {
      const response = await this.makeRequest('/usage-stats')
      return response.data
    } catch (error) {
      console.error('Error getting usage stats:', error)
      throw error
    }
  }

  // Check if AI features are available
  async checkAvailability() {
    try {
      const stats = await this.getUsageStats()
      return {
        available: stats.remainingRequests > 0,
        remainingRequests: stats.remainingRequests,
        monthlyLimit: stats.monthlyLimit,
        isPremium: stats.isPremium
      }
    } catch (error) {
      return {
        available: false,
        remainingRequests: 0,
        monthlyLimit: 0,
        isPremium: false
      }
    }
  }

  // Format error messages for user display
  formatError(error) {
    if (error.message.includes('Rate limit')) {
      return 'You\'ve reached your AI usage limit. Please try again later or upgrade to premium.'
    }
    
    if (error.message.includes('unauthorized')) {
      return 'Please log in to use AI features.'
    }
    
    if (error.message.includes('temporarily unavailable')) {
      return 'AI service is temporarily unavailable. Please try again in a few minutes.'
    }
    
    return 'Something went wrong with the AI enhancement. Please try again.'
  }
}

export default new AIService()
