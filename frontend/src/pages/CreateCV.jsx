import { useState } from 'react'

const CreateCV = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [cvData, setCvData] = useState({
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      summary: ''
    }
  })

  // Available templates with distinct designs
  const templates = [
    {
      id: 'modern-one-column',
      name: 'Modern One Column',
      description: 'Clean single-column layout with modern typography',
      layout: 'one-column',
      colors: { primary: '#2563eb', secondary: '#64748b', accent: '#f8fafc' },
      features: ['ATS-Friendly', 'Single Column', 'Modern Typography']
    },
    {
      id: 'professional-two-column',
      name: 'Professional Two Column',
      description: 'Classic two-column design with sidebar',
      layout: 'two-column',
      colors: { primary: '#1f2937', secondary: '#6b7280', accent: '#f3f4f6' },
      features: ['Two Column', 'Sidebar Layout', 'Professional']
    },
    {
      id: 'creative-modern',
      name: 'Creative Modern',
      description: 'Bold design with creative elements for designers',
      layout: 'creative',
      colors: { primary: '#7c3aed', secondary: '#a855f7', accent: '#faf5ff' },
      features: ['Creative Layout', 'Bold Design', 'Designer-Focused']
    },
    {
      id: 'executive-classic',
      name: 'Executive Classic',
      description: 'Traditional formal design for senior positions',
      layout: 'classic',
      colors: { primary: '#059669', secondary: '#6b7280', accent: '#f0fdf4' },
      features: ['Classic Design', 'Formal Layout', 'Executive Level']
    },
    {
      id: 'minimal-clean',
      name: 'Minimal Clean',
      description: 'Ultra-clean design focusing on content',
      layout: 'minimal',
      colors: { primary: '#374151', secondary: '#9ca3af', accent: '#ffffff' },
      features: ['Minimal Design', 'Content-Focused', 'Clean Lines']
    },
    {
      id: 'tech-developer',
      name: 'Tech Developer',
      description: 'Modern tech-focused design with code elements',
      layout: 'tech',
      colors: { primary: '#0891b2', secondary: '#0e7490', accent: '#ecfeff' },
      features: ['Tech-Focused', 'Code Elements', 'Developer-Friendly']
    }
  ]

  const steps = ['Choose Template', 'Personal Info', 'Experience', 'Education', 'Skills', 'Review']

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template)
  }

  const nextStep = () => {
    if (currentStep === 0 && !selectedTemplate) {
      alert('Please select a template first!')
      return
    }
    setCurrentStep(prev => Math.min(prev + 1, steps.length - 1))
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0))
  }

  const updatePersonalInfo = (field, value) => {
    setCvData(prev => ({
      ...prev,
      personalInfo: { ...prev.personalInfo, [field]: value }
    }))
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header with Progress */}
      <div className="bg-white shadow-sm border-b flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Create Your CV</h1>
            <div className="flex items-center space-x-4">
              {steps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index === currentStep ? 'bg-blue-600 text-white' :
                    index < currentStep ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-500'
                  }`}>
                    {index < currentStep ? '✓' : index + 1}
                  </div>
                  <span className={`ml-2 text-sm ${
                    index === currentStep ? 'text-blue-600 font-medium' : 'text-gray-500'
                  }`}>
                    {step}
                  </span>
                  {index < steps.length - 1 && <div className="w-8 h-0.5 bg-gray-300 ml-4"></div>}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Split Screen */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Form */}
        <div className="flex-1 overflow-y-auto">
          <div className="py-8 px-4 sm:px-6 lg:px-8">
            {currentStep === 0 && (
              <div className="space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Your Template</h2>
                  <p className="text-gray-600">Select a professional template to get started</p>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className={`cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                        selectedTemplate?.id === template.id
                          ? 'border-blue-500 bg-blue-50 shadow-lg'
                          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                      }`}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      {/* Template Preview */}
                      <div className="p-4 relative">
                        <div className="h-40 bg-white rounded border overflow-hidden">
                          <div className="p-3 space-y-2">
                            <div className="h-2 rounded" style={{ backgroundColor: template.colors.primary, width: '60%' }}></div>
                            <div className="h-1 rounded" style={{ backgroundColor: template.colors.secondary, width: '40%' }}></div>
                            <div className="space-y-1 mt-3">
                              <div className="h-1 rounded" style={{ backgroundColor: template.colors.secondary }}></div>
                              <div className="h-1 rounded" style={{ backgroundColor: template.colors.secondary, width: '80%' }}></div>
                              <div className="h-1 rounded" style={{ backgroundColor: template.colors.secondary, width: '60%' }}></div>
                            </div>
                          </div>
                        </div>
                        {selectedTemplate?.id === template.id && (
                          <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        )}
                      </div>

                      {/* Template Info */}
                      <div className="p-4 border-t">
                        <h3 className="font-semibold text-gray-900 mb-1">{template.name}</h3>
                        <p className="text-sm text-gray-600 mb-2">{template.description}</p>
                        <div className="flex flex-wrap gap-1">
                          {template.features.map((feature, index) => (
                            <span key={index} className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {selectedTemplate && (
                  <div className="text-center">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 inline-block">
                      <p className="text-green-800">✓ Selected: <strong>{selectedTemplate.name}</strong></p>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {currentStep === 1 && (
              <div className="max-w-2xl mx-auto">
                <div className="text-center mb-8">
                  <h2 className="text-3xl font-bold text-gray-900 mb-4">Personal Information</h2>
                  <p className="text-lg text-gray-600">Tell us about yourself</p>
                </div>

                <div className="bg-white rounded-lg shadow p-6 space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      value={cvData.personalInfo.fullName}
                      onChange={(e) => updatePersonalInfo('fullName', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input
                      type="email"
                      value={cvData.personalInfo.email}
                      onChange={(e) => updatePersonalInfo('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      value={cvData.personalInfo.phone}
                      onChange={(e) => updatePersonalInfo('phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="+****************"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={cvData.personalInfo.location}
                      onChange={(e) => updatePersonalInfo('location', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="City, Country"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Professional Summary</label>
                    <textarea
                      value={cvData.personalInfo.summary}
                      onChange={(e) => updatePersonalInfo('summary', e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Brief summary of your professional background and goals..."
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep > 1 && (
              <div className="max-w-2xl mx-auto text-center">
                <h2 className="text-3xl font-bold text-gray-900 mb-4">{steps[currentStep]}</h2>
                <div className="bg-white rounded-lg shadow p-8">
                  <p className="text-lg text-gray-600 mb-4">
                    This step will be implemented next: <strong>{steps[currentStep]}</strong>
                  </p>
                  <p className="text-gray-500">For now, you can navigate between steps to see the flow.</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - CV Preview */}
        <div className="w-80 bg-gray-100 border-l border-gray-200 overflow-y-auto">
          <div className="p-4">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">Live Preview</h3>
              {selectedTemplate && (
                <p className="text-sm text-gray-600">Template: {selectedTemplate.name}</p>
              )}
            </div>
            <div className="transform scale-75 origin-top">
              <div className="bg-white shadow-lg rounded-lg overflow-hidden" style={{ aspectRatio: '8.5/11' }}>
                <div className="p-4 text-xs h-full overflow-hidden">
                  {selectedTemplate ? (
                    <div className="space-y-3">
                      <div className="text-center border-b pb-3">
                        <h1 className="font-bold text-lg mb-1" style={{ color: selectedTemplate.colors.primary }}>
                          {cvData.personalInfo.fullName || 'Your Name'}
                        </h1>
                        <p className="text-xs" style={{ color: selectedTemplate.colors.secondary }}>
                          {cvData.personalInfo.email || '<EMAIL>'} • {cvData.personalInfo.phone || '+****************'}
                        </p>
                      </div>
                      
                      {cvData.personalInfo.summary && (
                        <div>
                          <h2 className="font-semibold text-sm mb-1" style={{ color: selectedTemplate.colors.primary }}>Summary</h2>
                          <p className="text-xs" style={{ color: selectedTemplate.colors.secondary }}>{cvData.personalInfo.summary}</p>
                        </div>
                      )}
                      
                      <div>
                        <h2 className="font-semibold text-sm mb-2" style={{ color: selectedTemplate.colors.primary }}>Experience</h2>
                        <div className="space-y-2">
                          <div>
                            <h3 className="font-semibold text-xs">Senior Software Engineer</h3>
                            <p className="text-xs" style={{ color: selectedTemplate.colors.secondary }}>Tech Company • 2020 - Present</p>
                            <p className="text-xs mt-1" style={{ color: selectedTemplate.colors.secondary }}>
                              Led development of key features and mentored junior developers.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center text-gray-500">
                        <svg className="w-16 h-16 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                        </svg>
                        <p>Select a template to see preview</p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white border-t px-4 sm:px-6 lg:px-8 py-4 flex-shrink-0">
        <div className="max-w-7xl mx-auto flex justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`px-6 py-2 rounded-md font-medium ${
              currentStep === 0
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            Previous
          </button>
          
          <button
            onClick={nextStep}
            className="px-6 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700"
          >
            {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default CreateCV
