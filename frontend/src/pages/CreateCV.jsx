import { useState } from 'react'

const CreateCV = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [cvData, setCvData] = useState({
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      summary: ''
    },
    experience: [],
    education: [],
    skills: []
  })

  // Available templates
  const templates = [
    {
      id: 'modern',
      name: 'Modern Professional',
      description: 'Clean and professional design perfect for corporate roles',
      color: 'blue',
      preview: 'bg-blue-500'
    },
    {
      id: 'creative',
      name: 'Creative Designer',
      description: 'Perfect for designers and creative professionals',
      color: 'purple',
      preview: 'bg-purple-500'
    },
    {
      id: 'minimal',
      name: 'Minimal Clean',
      description: 'Simple and elegant design that focuses on content',
      color: 'gray',
      preview: 'bg-gray-500'
    },
    {
      id: 'executive',
      name: 'Executive Premium',
      description: 'Premium design for senior-level positions',
      color: 'green',
      preview: 'bg-green-500'
    }
  ]

  const steps = [
    'Choose Template',
    'Personal Info',
    'Experience',
    'Education',
    'Skills',
    'Review'
  ]

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template)
  }

  const nextStep = () => {
    if (currentStep === 0 && !selectedTemplate) {
      alert('Please select a template first!')
      return
    }
    setCurrentStep(prev => Math.min(prev + 1, steps.length - 1))
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0))
  }

  const updatePersonalInfo = (field, value) => {
    setCvData(prev => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        [field]: value
      }
    }))
  }

  // Step 0: Template Selection
  const renderTemplateSelection = () => (
    <div className="max-w-6xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Choose Your Template</h2>
        <p className="text-lg text-gray-600">Select a professional template to get started</p>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        {templates.map((template) => (
          <div
            key={template.id}
            className={`cursor-pointer rounded-lg border-2 transition-all duration-200 ${
              selectedTemplate?.id === template.id
                ? 'border-blue-500 bg-blue-50 shadow-lg'
                : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
            }`}
            onClick={() => handleTemplateSelect(template)}
          >
            {/* Template Preview */}
            <div className={`h-48 ${template.preview} rounded-t-lg relative overflow-hidden`}>
              <div className="absolute inset-0 p-4 text-white">
                <div className="bg-white/20 rounded p-2 mb-2">
                  <div className="h-2 bg-white/60 rounded mb-1"></div>
                  <div className="h-1 bg-white/40 rounded w-3/4"></div>
                </div>
                <div className="space-y-1">
                  <div className="h-1 bg-white/40 rounded"></div>
                  <div className="h-1 bg-white/40 rounded w-5/6"></div>
                  <div className="h-1 bg-white/40 rounded w-4/6"></div>
                </div>
              </div>
              {selectedTemplate?.id === template.id && (
                <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                </div>
              )}
            </div>

            {/* Template Info */}
            <div className="p-4">
              <h3 className="font-semibold text-gray-900 mb-2">{template.name}</h3>
              <p className="text-sm text-gray-600">{template.description}</p>
            </div>
          </div>
        ))}
      </div>

      {selectedTemplate && (
        <div className="text-center">
          <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 inline-block">
            <p className="text-green-800">
              ✓ Selected: <strong>{selectedTemplate.name}</strong>
            </p>
          </div>
        </div>
      )}
    </div>
  )

  // Step 1: Personal Information
  const renderPersonalInfo = () => (
    <div className="max-w-2xl mx-auto">
      <div className="text-center mb-8">
        <h2 className="text-3xl font-bold text-gray-900 mb-4">Personal Information</h2>
        <p className="text-lg text-gray-600">Tell us about yourself</p>
      </div>

      <div className="bg-white rounded-lg shadow p-6 space-y-6">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
          <input
            type="text"
            value={cvData.personalInfo.fullName}
            onChange={(e) => updatePersonalInfo('fullName', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Enter your full name"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
          <input
            type="email"
            value={cvData.personalInfo.email}
            onChange={(e) => updatePersonalInfo('email', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="<EMAIL>"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
          <input
            type="tel"
            value={cvData.personalInfo.phone}
            onChange={(e) => updatePersonalInfo('phone', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="+****************"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
          <input
            type="text"
            value={cvData.personalInfo.location}
            onChange={(e) => updatePersonalInfo('location', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="City, Country"
          />
        </div>

        <div>
          <label className="block text-sm font-medium text-gray-700 mb-2">Professional Summary</label>
          <textarea
            value={cvData.personalInfo.summary}
            onChange={(e) => updatePersonalInfo('summary', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            placeholder="Brief summary of your professional background and goals..."
          />
        </div>
      </div>
    </div>
  )

  // Placeholder for other steps
  const renderOtherSteps = () => (
    <div className="max-w-2xl mx-auto text-center">
      <h2 className="text-3xl font-bold text-gray-900 mb-4">{steps[currentStep]}</h2>
      <div className="bg-white rounded-lg shadow p-8">
        <p className="text-lg text-gray-600 mb-4">
          This step will be implemented next: <strong>{steps[currentStep]}</strong>
        </p>
        <p className="text-gray-500">
          For now, you can navigate between steps to see the flow.
        </p>
      </div>
    </div>
  )

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header with Progress */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Create Your CV</h1>

            {/* Progress Steps */}
            <div className="flex items-center space-x-4">
              {steps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index === currentStep
                      ? 'bg-blue-600 text-white'
                      : index < currentStep
                      ? 'bg-green-500 text-white'
                      : 'bg-gray-300 text-gray-500'
                  }`}>
                    {index < currentStep ? '✓' : index + 1}
                  </div>
                  <span className={`ml-2 text-sm ${
                    index === currentStep ? 'text-blue-600 font-medium' : 'text-gray-500'
                  }`}>
                    {step}
                  </span>
                  {index < steps.length - 1 && (
                    <div className="w-8 h-0.5 bg-gray-300 ml-4"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="py-8 px-4 sm:px-6 lg:px-8">
        {currentStep === 0 && renderTemplateSelection()}
        {currentStep === 1 && renderPersonalInfo()}
        {currentStep > 1 && renderOtherSteps()}
      </div>

      {/* Navigation */}
      <div className="bg-white border-t px-4 sm:px-6 lg:px-8 py-4">
        <div className="max-w-7xl mx-auto flex justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`px-6 py-2 rounded-md font-medium ${
              currentStep === 0
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            Previous
          </button>

          <button
            onClick={nextStep}
            className="px-6 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700"
          >
            {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default CreateCV