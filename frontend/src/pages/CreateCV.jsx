import { useState } from 'react'

const CreateCV = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [selectedColors, setSelectedColors] = useState(null)
  const [selectedColorName, setSelectedColorName] = useState(null)
  const [cvData, setCvData] = useState({
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      summary: ''
    }
  })

  // Available templates with distinct layouts
  const templates = [
    {
      id: 'simple-one-column',
      name: 'Simple One Column',
      description: 'Clean single-column layout perfect for any profession',
      layout: 'simple-one-column',
      defaultColors: { primary: '#2563eb', secondary: '#64748b', accent: '#f8fafc' },
      colorOptions: [
        { name: 'Blue', colors: { primary: '#2563eb', secondary: '#64748b', accent: '#f8fafc' } },
        { name: '<PERSON>', colors: { primary: '#059669', secondary: '#6b7280', accent: '#f0fdf4' } },
        { name: 'Purple', colors: { primary: '#7c3aed', secondary: '#a855f7', accent: '#faf5ff' } },
        { name: 'Red', colors: { primary: '#dc2626', secondary: '#6b7280', accent: '#fef2f2' } }
      ],
      features: ['ATS-Friendly', 'Single Column', 'Clean Design']
    },
    {
      id: 'professional-sidebar',
      name: 'Professional Sidebar',
      description: 'Two-column design with left sidebar for contact info',
      layout: 'professional-sidebar',
      defaultColors: { primary: '#1f2937', secondary: '#6b7280', accent: '#f3f4f6' },
      colorOptions: [
        { name: 'Dark', colors: { primary: '#1f2937', secondary: '#6b7280', accent: '#f3f4f6' } },
        { name: 'Navy', colors: { primary: '#1e40af', secondary: '#64748b', accent: '#eff6ff' } },
        { name: 'Teal', colors: { primary: '#0f766e', secondary: '#6b7280', accent: '#f0fdfa' } },
        { name: 'Indigo', colors: { primary: '#4338ca', secondary: '#6b7280', accent: '#eef2ff' } }
      ],
      features: ['Two Column', 'Sidebar Layout', 'Professional']
    },
    {
      id: 'modern-header',
      name: 'Modern Header',
      description: 'Bold header design with modern typography',
      layout: 'modern-header',
      defaultColors: { primary: '#7c3aed', secondary: '#a855f7', accent: '#faf5ff' },
      colorOptions: [
        { name: 'Purple', colors: { primary: '#7c3aed', secondary: '#a855f7', accent: '#faf5ff' } },
        { name: 'Orange', colors: { primary: '#ea580c', secondary: '#fb923c', accent: '#fff7ed' } },
        { name: 'Pink', colors: { primary: '#db2777', secondary: '#f472b6', accent: '#fdf2f8' } },
        { name: 'Emerald', colors: { primary: '#059669', secondary: '#34d399', accent: '#ecfdf5' } }
      ],
      features: ['Bold Header', 'Modern Design', 'Eye-catching']
    },
    {
      id: 'creative-blocks',
      name: 'Creative Blocks',
      description: 'Creative layout with colored blocks and sections',
      layout: 'creative-blocks',
      defaultColors: { primary: '#0891b2', secondary: '#0e7490', accent: '#ecfeff' },
      colorOptions: [
        { name: 'Cyan', colors: { primary: '#0891b2', secondary: '#0e7490', accent: '#ecfeff' } },
        { name: 'Violet', colors: { primary: '#8b5cf6', secondary: '#a78bfa', accent: '#f5f3ff' } },
        { name: 'Rose', colors: { primary: '#e11d48', secondary: '#fb7185', accent: '#fff1f2' } },
        { name: 'Amber', colors: { primary: '#d97706', secondary: '#fbbf24', accent: '#fffbeb' } }
      ],
      features: ['Creative Layout', 'Color Blocks', 'Designer-Focused']
    },
    {
      id: 'minimal-lines',
      name: 'Minimal Lines',
      description: 'Ultra-clean design with subtle line separators',
      layout: 'minimal-lines',
      defaultColors: { primary: '#374151', secondary: '#9ca3af', accent: '#ffffff' },
      colorOptions: [
        { name: 'Gray', colors: { primary: '#374151', secondary: '#9ca3af', accent: '#ffffff' } },
        { name: 'Slate', colors: { primary: '#475569', secondary: '#94a3b8', accent: '#f8fafc' } },
        { name: 'Stone', colors: { primary: '#57534e', secondary: '#a8a29e', accent: '#fafaf9' } },
        { name: 'Zinc', colors: { primary: '#52525b', secondary: '#a1a1aa', accent: '#fafafa' } }
      ],
      features: ['Minimal Design', 'Clean Lines', 'Content-Focused']
    },
    {
      id: 'executive-formal',
      name: 'Executive Formal',
      description: 'Traditional formal design for senior positions',
      layout: 'executive-formal',
      defaultColors: { primary: '#1f2937', secondary: '#6b7280', accent: '#f9fafb' },
      colorOptions: [
        { name: 'Classic', colors: { primary: '#1f2937', secondary: '#6b7280', accent: '#f9fafb' } },
        { name: 'Navy', colors: { primary: '#1e3a8a', secondary: '#64748b', accent: '#f1f5f9' } },
        { name: 'Forest', colors: { primary: '#14532d', secondary: '#6b7280', accent: '#f7fef7' } },
        { name: 'Burgundy', colors: { primary: '#7f1d1d', secondary: '#6b7280', accent: '#fef2f2' } }
      ],
      features: ['Executive Level', 'Formal Layout', 'Traditional']
    }
  ]

  const steps = ['Choose Template', 'Personal Info', 'Experience', 'Education', 'Skills', 'Review']

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template)
    setSelectedColors(template.defaultColors)
    setSelectedColorName(template.colorOptions[0].name) // Set first color as default
  }

  const handleColorChange = (colors, colorName) => {
    console.log('Color changed to:', colorName, colors)
    console.log('Previous colors:', selectedColors)
    setSelectedColors(colors)
    setSelectedColorName(colorName)
    console.log('New colors set:', colors)
  }

  const nextStep = () => {
    if (currentStep === 0 && !selectedTemplate) {
      alert('Please select a template first!')
      return
    }
    setCurrentStep(prev => Math.min(prev + 1, steps.length - 1))
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0))
  }

  const updatePersonalInfo = (field, value) => {
    setCvData(prev => ({
      ...prev,
      personalInfo: { ...prev.personalInfo, [field]: value }
    }))
  }

  // Different layout renderers for CV preview
  const renderCVLayout = () => {
    if (!selectedTemplate || !selectedColors) return null

    const colors = selectedColors
    const { personalInfo } = cvData

    console.log('Rendering CV with colors:', colors)
    console.log('Template layout:', selectedTemplate.layout)

    switch (selectedTemplate.layout) {
      case 'simple-one-column':
        return (
          <div className="p-6 space-y-4" style={{ backgroundColor: colors.accent }}>
            {/* Debug color bar */}
            <div className="h-2 w-full rounded mb-4" style={{ backgroundColor: colors.primary }}></div>
            <div className="text-center pb-4 border-b-2" style={{ borderColor: colors.primary }}>
              <h1 className="text-2xl font-bold mb-2" style={{ color: colors.primary }}>
                {personalInfo.fullName || 'Your Name'}
              </h1>
              <div className="text-sm space-y-1" style={{ color: colors.secondary }}>
                <p>{personalInfo.email || '<EMAIL>'}</p>
                <p>{personalInfo.phone || '+****************'}</p>
                <p>{personalInfo.location || 'City, Country'}</p>
              </div>
            </div>
            {personalInfo.summary && (
              <div className="p-4 rounded" style={{ backgroundColor: 'white' }}>
                <h2 className="text-lg font-semibold mb-2" style={{ color: colors.primary }}>Summary</h2>
                <p className="text-sm" style={{ color: colors.secondary }}>{personalInfo.summary}</p>
              </div>
            )}
            <div className="p-4 rounded" style={{ backgroundColor: 'white' }}>
              <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Experience</h2>
              <div className="space-y-3">
                <div className="border-l-4 pl-3" style={{ borderColor: colors.primary }}>
                  <h3 className="font-semibold" style={{ color: colors.primary }}>Senior Software Engineer</h3>
                  <p className="text-sm" style={{ color: colors.secondary }}>Tech Company • 2020 - Present</p>
                  <p className="text-sm mt-1" style={{ color: colors.secondary }}>Led development of key features and mentored junior developers.</p>
                </div>
                <div className="border-l-4 pl-3" style={{ borderColor: colors.primary }}>
                  <h3 className="font-semibold" style={{ color: colors.primary }}>Software Engineer</h3>
                  <p className="text-sm" style={{ color: colors.secondary }}>Previous Company • 2018 - 2020</p>
                  <p className="text-sm mt-1" style={{ color: colors.secondary }}>Developed web applications using modern technologies.</p>
                </div>
              </div>
            </div>
            <div className="p-4 rounded" style={{ backgroundColor: 'white' }}>
              <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Education</h2>
              <div className="border-l-4 pl-3" style={{ borderColor: colors.primary }}>
                <h3 className="font-semibold" style={{ color: colors.primary }}>Bachelor of Computer Science</h3>
                <p className="text-sm" style={{ color: colors.secondary }}>University Name • 2014 - 2018</p>
              </div>
            </div>
            <div className="p-4 rounded" style={{ backgroundColor: 'white' }}>
              <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Skills</h2>
              <div className="flex flex-wrap gap-2">
                {['JavaScript', 'React', 'Node.js', 'Python', 'SQL', 'Git'].map((skill, i) => (
                  <span key={i} className="px-2 py-1 text-xs rounded" style={{
                    backgroundColor: colors.primary,
                    color: 'white'
                  }}>
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          </div>
        )

      case 'professional-sidebar':
        return (
          <div className="flex h-full">
            <div className="w-1/3 p-4 space-y-4" style={{ backgroundColor: colors.accent }}>
              <div className="text-center">
                <div className="w-20 h-20 rounded-full mx-auto mb-3" style={{ backgroundColor: colors.primary }}></div>
                <h1 className="text-lg font-bold" style={{ color: colors.primary }}>
                  {personalInfo.fullName || 'Your Name'}
                </h1>
              </div>
              <div>
                <h3 className="font-semibold text-sm mb-2" style={{ color: colors.primary }}>Contact</h3>
                <div className="text-xs space-y-1" style={{ color: colors.secondary }}>
                  <p>{personalInfo.email || '<EMAIL>'}</p>
                  <p>{personalInfo.phone || '+****************'}</p>
                  <p>{personalInfo.location || 'City, Country'}</p>
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-sm mb-2" style={{ color: colors.primary }}>Skills</h3>
                <div className="space-y-1">
                  {['JavaScript', 'React', 'Node.js', 'Python', 'SQL', 'Git'].map((skill, i) => (
                    <div key={i} className="text-xs px-2 py-1 rounded mb-1" style={{
                      backgroundColor: colors.primary,
                      color: 'white'
                    }}>
                      {skill}
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-sm mb-2" style={{ color: colors.primary }}>Education</h3>
                <div className="text-xs space-y-1" style={{ color: colors.secondary }}>
                  <p className="font-medium">Bachelor of CS</p>
                  <p>University • 2014-2018</p>
                </div>
              </div>
            </div>
            <div className="flex-1 p-4 space-y-4">
              {personalInfo.summary && (
                <div>
                  <h2 className="text-lg font-semibold mb-2" style={{ color: colors.primary }}>Summary</h2>
                  <p className="text-sm" style={{ color: colors.secondary }}>{personalInfo.summary}</p>
                </div>
              )}
              <div>
                <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Experience</h2>
                <div className="space-y-3">
                  <div className="border-l-3 pl-3" style={{ borderColor: colors.primary }}>
                    <h3 className="font-semibold" style={{ color: colors.primary }}>Senior Software Engineer</h3>
                    <p className="text-sm" style={{ color: colors.secondary }}>Tech Company • 2020 - Present</p>
                    <p className="text-sm mt-1" style={{ color: colors.secondary }}>Led development of key features and mentored junior developers.</p>
                  </div>
                  <div className="border-l-3 pl-3" style={{ borderColor: colors.primary }}>
                    <h3 className="font-semibold" style={{ color: colors.primary }}>Software Engineer</h3>
                    <p className="text-sm" style={{ color: colors.secondary }}>Previous Company • 2018 - 2020</p>
                    <p className="text-sm mt-1" style={{ color: colors.secondary }}>Developed web applications using modern technologies.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      case 'modern-header':
        return (
          <div>
            <div className="p-6 text-white text-center" style={{ backgroundColor: colors.primary }}>
              <h1 className="text-2xl font-bold mb-2">{personalInfo.fullName || 'Your Name'}</h1>
              <p className="text-sm opacity-90">{personalInfo.email || '<EMAIL>'} • {personalInfo.phone || '+****************'}</p>
              {/* Debug: Show color value */}
              <p className="text-xs opacity-75 mt-2">Color: {colors.primary}</p>
            </div>
            <div className="p-6 space-y-4" style={{ backgroundColor: colors.accent }}>
              {personalInfo.summary && (
                <div>
                  <h2 className="text-lg font-semibold mb-2" style={{ color: colors.primary }}>Summary</h2>
                  <p className="text-sm" style={{ color: colors.secondary }}>{personalInfo.summary}</p>
                </div>
              )}
              <div>
                <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Experience</h2>
                <div className="space-y-3">
                  <div className="p-3 rounded" style={{ backgroundColor: 'white', borderLeft: `4px solid ${colors.primary}` }}>
                    <h3 className="font-semibold" style={{ color: colors.primary }}>Senior Software Engineer</h3>
                    <p className="text-sm" style={{ color: colors.secondary }}>Tech Company • 2020 - Present</p>
                    <p className="text-sm mt-1" style={{ color: colors.secondary }}>Led development of key features and mentored junior developers.</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return (
          <div className="p-6 space-y-4">
            <div className="text-center border-b pb-4" style={{ borderColor: colors.secondary + '40' }}>
              <h1 className="text-2xl font-bold mb-2" style={{ color: colors.primary }}>
                {personalInfo.fullName || 'Your Name'}
              </h1>
              <p className="text-sm" style={{ color: colors.secondary }}>
                {personalInfo.email || '<EMAIL>'} • {personalInfo.phone || '+****************'}
              </p>
            </div>
            <div>
              <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Experience</h2>
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold">Senior Software Engineer</h3>
                  <p className="text-sm" style={{ color: colors.secondary }}>Tech Company • 2020 - Present</p>
                </div>
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header with Progress */}
      <div className="bg-white shadow-sm border-b flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Create Your CV</h1>
            <div className="flex items-center space-x-4">
              {steps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index === currentStep ? 'bg-blue-600 text-white' :
                    index < currentStep ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-500'
                  }`}>
                    {index < currentStep ? '✓' : index + 1}
                  </div>
                  <span className={`ml-2 text-sm ${
                    index === currentStep ? 'text-blue-600 font-medium' : 'text-gray-500'
                  }`}>
                    {step}
                  </span>
                  {index < steps.length - 1 && <div className="w-8 h-0.5 bg-gray-300 ml-4"></div>}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Split Screen */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Form (50% width) */}
        <div className="w-1/2 overflow-y-auto border-r border-gray-200">
          <div className="py-8 px-6">
            {currentStep === 0 && (
              <div className="space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Your Template</h2>
                  <p className="text-gray-600">Select a professional template to get started</p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className={`cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                        selectedTemplate?.id === template.id
                          ? 'border-blue-500 bg-blue-50 shadow-lg'
                          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                      }`}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      {/* Template Preview */}
                      <div className="p-4 relative">
                        <div className="h-40 bg-white rounded border overflow-hidden">
                          {template.layout === 'professional-sidebar' ? (
                            <div className="flex h-full">
                              <div className="w-1/3 p-2" style={{ backgroundColor: template.defaultColors.accent }}>
                                <div className="w-4 h-4 rounded-full mx-auto mb-1" style={{ backgroundColor: template.defaultColors.primary }}></div>
                                <div className="space-y-0.5">
                                  <div className="h-0.5 w-full rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                  <div className="h-0.5 w-3/4 rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                </div>
                              </div>
                              <div className="flex-1 p-2 space-y-1">
                                <div className="h-1 w-3/4 rounded" style={{ backgroundColor: template.defaultColors.primary }}></div>
                                <div className="h-0.5 w-full rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                <div className="h-0.5 w-5/6 rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                              </div>
                            </div>
                          ) : template.layout === 'modern-header' ? (
                            <div>
                              <div className="h-8 w-full flex items-center justify-center" style={{ backgroundColor: template.defaultColors.primary }}>
                                <div className="h-1 w-16 bg-white rounded"></div>
                              </div>
                              <div className="p-2 space-y-1">
                                <div className="h-0.5 w-full rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                <div className="h-0.5 w-4/5 rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                              </div>
                            </div>
                          ) : (
                            <div className="p-3 space-y-2">
                              <div className="h-2 rounded" style={{ backgroundColor: template.defaultColors.primary, width: '60%' }}></div>
                              <div className="h-1 rounded" style={{ backgroundColor: template.defaultColors.secondary, width: '40%' }}></div>
                              <div className="space-y-1 mt-3">
                                <div className="h-1 rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                <div className="h-1 rounded" style={{ backgroundColor: template.defaultColors.secondary, width: '80%' }}></div>
                                <div className="h-1 rounded" style={{ backgroundColor: template.defaultColors.secondary, width: '60%' }}></div>
                              </div>
                            </div>
                          )}
                        </div>
                        {selectedTemplate?.id === template.id && (
                          <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        )}
                      </div>

                      {/* Template Info */}
                      <div className="p-4 border-t">
                        <h3 className="font-semibold text-gray-900 mb-1">{template.name}</h3>
                        <p className="text-sm text-gray-600 mb-3">{template.description}</p>

                        {/* Color Options */}
                        {selectedTemplate?.id === template.id && (
                          <div className="mb-3">
                            <p className="text-xs font-medium text-gray-700 mb-2">Choose Color:</p>
                            <div className="flex gap-2">
                              {template.colorOptions.map((colorOption, index) => (
                                <button
                                  key={index}
                                  onClick={() => {
                                    console.log('Button clicked:', colorOption.name, colorOption.colors)
                                    handleColorChange(colorOption.colors, colorOption.name)
                                  }}
                                  className={`w-6 h-6 rounded-full border-2 transition-all ${
                                    selectedColorName === colorOption.name
                                      ? 'border-gray-800 scale-110 shadow-lg'
                                      : 'border-gray-300 hover:border-gray-500'
                                  }`}
                                  style={{ backgroundColor: colorOption.colors.primary }}
                                  title={colorOption.name}
                                />
                              ))}
                            </div>
                            <div className="mt-2 text-xs text-gray-500">
                              <p>Current: {selectedColorName || 'None'}</p>
                              <p>Primary: {selectedColors?.primary || 'None'}</p>
                            </div>
                            <div className="mt-2">
                              <p className="text-xs text-gray-500">Selected: {selectedColorName}</p>
                              <button
                                onClick={() => {
                                  console.log('Force color change test')
                                  setSelectedColors({ primary: '#ff0000', secondary: '#666666', accent: '#ffeeee' })
                                  setSelectedColorName('Test Red')
                                }}
                                className="mt-1 px-2 py-1 text-xs bg-red-500 text-white rounded"
                              >
                                Test Red
                              </button>
                            </div>
                          </div>
                        )}

                        <div className="flex flex-wrap gap-1">
                          {template.features.map((feature, index) => (
                            <span key={index} className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {selectedTemplate && (
                  <div className="text-center">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 inline-block">
                      <p className="text-green-800">✓ Selected: <strong>{selectedTemplate.name}</strong></p>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {currentStep === 1 && (
              <div>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Personal Information</h2>
                  <p className="text-gray-600">Tell us about yourself and see the live preview update</p>
                </div>

                <div className="bg-white rounded-lg shadow p-6 space-y-6">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      value={cvData.personalInfo.fullName}
                      onChange={(e) => updatePersonalInfo('fullName', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input
                      type="email"
                      value={cvData.personalInfo.email}
                      onChange={(e) => updatePersonalInfo('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      value={cvData.personalInfo.phone}
                      onChange={(e) => updatePersonalInfo('phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="+****************"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={cvData.personalInfo.location}
                      onChange={(e) => updatePersonalInfo('location', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="City, Country"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Professional Summary</label>
                    <textarea
                      value={cvData.personalInfo.summary}
                      onChange={(e) => updatePersonalInfo('summary', e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Brief summary of your professional background and goals..."
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep > 1 && (
              <div>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">{steps[currentStep]}</h2>
                  <p className="text-gray-600">This section will be implemented next. You can see your CV preview on the right.</p>
                </div>
                <div className="bg-white rounded-lg shadow p-6">
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Coming Soon</h3>
                    <p className="text-gray-600 mb-4">
                      The <strong>{steps[currentStep]}</strong> section will be available in the next update.
                    </p>
                    <p className="text-sm text-gray-500">
                      For now, you can navigate between steps and see how your CV looks in the live preview.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - CV Preview (50% width) */}
        <div className="w-1/2 bg-gray-100 overflow-y-auto">
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Live Preview</h3>
              {selectedTemplate && (
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">Template: {selectedTemplate.name}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-xs text-gray-500">Color: {selectedColorName || 'Default'}</p>
                    {selectedColors && (
                      <div
                        className="w-4 h-4 rounded border border-gray-300"
                        style={{ backgroundColor: selectedColors.primary }}
                        title={`Primary: ${selectedColors.primary}`}
                      ></div>
                    )}
                  </div>
                </div>
              )}
            </div>
            <div className="flex justify-center">
              <div className="w-full max-w-md">
                <div className="bg-white shadow-xl rounded-lg overflow-hidden" style={{ aspectRatio: '8.5/11' }}>
                  <div className="text-sm h-full overflow-hidden" key={`${selectedTemplate?.id}-${selectedColorName}`}>
                    {selectedTemplate && selectedColors ? (
                      renderCVLayout()
                    ) : (
                      <div className="h-full flex items-center justify-center">
                        <div className="text-center text-gray-500 p-8">
                          <svg className="w-20 h-20 mx-auto mb-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <h4 className="text-lg font-medium text-gray-700 mb-2">CV Preview</h4>
                          <p className="text-sm">Select a template to see your CV preview</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white border-t px-4 sm:px-6 lg:px-8 py-4 flex-shrink-0">
        <div className="max-w-7xl mx-auto flex justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`px-6 py-2 rounded-md font-medium ${
              currentStep === 0
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            Previous
          </button>
          
          <button
            onClick={nextStep}
            className="px-6 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700"
          >
            {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default CreateCV
