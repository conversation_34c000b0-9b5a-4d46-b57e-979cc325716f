import { useState } from 'react'

const CreateCV = () => {
  const [currentStep, setCurrentStep] = useState(1)
  const [cvData, setCvData] = useState({
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      summary: ''
    },
    experience: [],
    education: [],
    skills: []
  })

  const steps = [
    { id: 1, name: 'Personal Info', icon: '👤' },
    { id: 2, name: 'Experience', icon: '💼' },
    { id: 3, name: 'Education', icon: '🎓' },
    { id: 4, name: 'Skills', icon: '⚡' },
    { id: 5, name: 'Review', icon: '✅' }
  ]

  const updatePersonalInfo = (field, value) => {
    setCvData(prev => ({
      ...prev,
      personalInfo: {
        ...prev.personalInfo,
        [field]: value
      }
    }))
  }

  const renderStepContent = () => {
    switch (currentStep) {
      case 1:
        return (
          <div className="space-y-6">
            <div>
              <h2 className="text-2xl font-bold text-gray-900 mb-2">Personal Information</h2>
              <p className="text-gray-600">Let's start with your basic information</p>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Full Name *
                </label>
                <input
                  type="text"
                  value={cvData.personalInfo.fullName}
                  onChange={(e) => updatePersonalInfo('fullName', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Enter your full name"
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Email Address *
                  </label>
                  <input
                    type="email"
                    value={cvData.personalInfo.email}
                    onChange={(e) => updatePersonalInfo('email', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Phone Number
                  </label>
                  <input
                    type="tel"
                    value={cvData.personalInfo.phone}
                    onChange={(e) => updatePersonalInfo('phone', e.target.value)}
                    className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                    placeholder="+****************"
                  />
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Location
                </label>
                <input
                  type="text"
                  value={cvData.personalInfo.location}
                  onChange={(e) => updatePersonalInfo('location', e.target.value)}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="City, Country"
                />
              </div>

              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Professional Summary
                </label>
                <textarea
                  value={cvData.personalInfo.summary}
                  onChange={(e) => updatePersonalInfo('summary', e.target.value)}
                  rows={4}
                  className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
                  placeholder="Write a brief summary of your professional background and career objectives..."
                />
              </div>
            </div>
          </div>
        )

      default:
        return (
          <div className="text-center py-12">
            <h3 className="text-lg font-medium text-gray-900">Step {currentStep}</h3>
            <p className="text-gray-500 mt-2">This step is coming soon...</p>
          </div>
        )
    }
  }

  const renderCVPreview = () => {
    return (
      <div className="bg-white shadow-lg rounded-lg overflow-hidden" style={{ aspectRatio: '8.5/11' }}>
        <div className="p-8 text-sm">
          {/* Header */}
          <div className="text-center border-b border-gray-200 pb-6 mb-6">
            <h1 className="text-2xl font-bold text-gray-900 mb-2">
              {cvData.personalInfo.fullName || 'Your Name'}
            </h1>
            <div className="text-gray-600 space-y-1">
              {cvData.personalInfo.email && (
                <div>{cvData.personalInfo.email}</div>
              )}
              {cvData.personalInfo.phone && (
                <div>{cvData.personalInfo.phone}</div>
              )}
              {cvData.personalInfo.location && (
                <div>{cvData.personalInfo.location}</div>
              )}
            </div>
          </div>

          {/* Professional Summary */}
          {cvData.personalInfo.summary && (
            <div className="mb-6">
              <h2 className="text-lg font-semibold text-gray-900 mb-3 border-b border-gray-200 pb-1">
                Professional Summary
              </h2>
              <p className="text-gray-700 leading-relaxed">
                {cvData.personalInfo.summary}
              </p>
            </div>
          )}

          {/* Experience Section */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3 border-b border-gray-200 pb-1">
              Work Experience
            </h2>
            {cvData.experience.length === 0 ? (
              <p className="text-gray-400 italic">No experience added yet</p>
            ) : (
              <div className="space-y-4">
                {cvData.experience.map((exp, index) => (
                  <div key={index}>
                    <h3 className="font-medium text-gray-900">{exp.position}</h3>
                    <p className="text-gray-600">{exp.company}</p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Education Section */}
          <div className="mb-6">
            <h2 className="text-lg font-semibold text-gray-900 mb-3 border-b border-gray-200 pb-1">
              Education
            </h2>
            {cvData.education.length === 0 ? (
              <p className="text-gray-400 italic">No education added yet</p>
            ) : (
              <div className="space-y-4">
                {cvData.education.map((edu, index) => (
                  <div key={index}>
                    <h3 className="font-medium text-gray-900">{edu.degree}</h3>
                    <p className="text-gray-600">{edu.institution}</p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Skills Section */}
          <div>
            <h2 className="text-lg font-semibold text-gray-900 mb-3 border-b border-gray-200 pb-1">
              Skills
            </h2>
            {cvData.skills.length === 0 ? (
              <p className="text-gray-400 italic">No skills added yet</p>
            ) : (
              <div className="flex flex-wrap gap-2">
                {cvData.skills.map((skill, index) => (
                  <span key={index} className="px-2 py-1 bg-gray-100 text-gray-700 rounded text-xs">
                    {skill}
                  </span>
                ))}
              </div>
            )}
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-xl font-bold text-gray-900">CV Builder</h1>
              <p className="text-sm text-gray-600">Create your professional CV</p>
            </div>

            {/* Step Progress */}
            <div className="flex items-center space-x-2">
              {steps.map((step, index) => (
                <div key={step.id} className="flex items-center">
                  <button
                    onClick={() => setCurrentStep(step.id)}
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-medium transition-colors ${
                      currentStep === step.id
                        ? 'bg-primary-600 text-white'
                        : currentStep > step.id
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-300 text-gray-600'
                    }`}
                  >
                    {currentStep > step.id ? '✓' : step.id}
                  </button>
                  <span className={`ml-1 text-xs ${
                    currentStep === step.id ? 'text-primary-600 font-medium' : 'text-gray-500'
                  }`}>
                    {step.name}
                  </span>
                  {index < steps.length - 1 && (
                    <div className="w-6 h-0.5 bg-gray-300 mx-2"></div>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Split Layout */}
      <div className="flex h-screen">
        {/* Left Side - Form */}
        <div className="w-1/2 overflow-y-auto">
          <div className="p-8">
            {renderStepContent()}

            {/* Navigation Buttons */}
            <div className="flex justify-between mt-8 pt-6 border-t border-gray-200">
              <button
                onClick={() => setCurrentStep(Math.max(1, currentStep - 1))}
                disabled={currentStep === 1}
                className={`px-6 py-2 rounded-lg font-medium ${
                  currentStep === 1
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-gray-200 text-gray-700 hover:bg-gray-300'
                }`}
              >
                Previous
              </button>

              <button
                onClick={() => setCurrentStep(Math.min(steps.length, currentStep + 1))}
                disabled={currentStep === steps.length}
                className={`px-6 py-2 rounded-lg font-medium ${
                  currentStep === steps.length
                    ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                    : 'bg-primary-600 text-white hover:bg-primary-700'
                }`}
              >
                {currentStep === steps.length ? 'Complete' : 'Next'}
              </button>
            </div>
          </div>
        </div>

        {/* Right Side - Live Preview */}
        <div className="w-1/2 bg-gray-100 overflow-y-auto">
          <div className="p-8">
            <div className="mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Live Preview</h3>
              <p className="text-sm text-gray-600">Your CV updates in real-time</p>
            </div>

            <div className="transform scale-90 origin-top">
              {renderCVPreview()}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default CreateCV
