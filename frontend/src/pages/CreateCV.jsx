import { useState } from 'react'

// Skill Input Component
const SkillInput = ({ onAddSkill }) => {
  const [skillInput, setSkillInput] = useState('')
  const [suggestions] = useState([
    'JavaScript', 'React', 'Node.js', 'Python', 'Java', 'TypeScript', 'HTML/CSS',
    'SQL', 'Git', 'AWS', 'Docker', 'MongoDB', 'Express.js', 'Vue.js', 'Angular',
    'Project Management', 'Leadership', 'Communication', 'Problem Solving',
    'Team Collaboration', 'Agile/Scrum', 'Data Analysis', 'UI/UX Design'
  ])

  const handleSubmit = (e) => {
    e.preventDefault()
    if (skillInput.trim()) {
      onAddSkill(skillInput.trim())
      setSkillInput('')
    }
  }

  const handleSuggestionClick = (suggestion) => {
    onAddSkill(suggestion)
  }

  const filteredSuggestions = suggestions.filter(suggestion =>
    suggestion.toLowerCase().includes(skillInput.toLowerCase()) && skillInput.length > 0
  )

  return (
    <div className="relative">
      <form onSubmit={handleSubmit} className="flex gap-2 mb-3">
        <input
          type="text"
          value={skillInput}
          onChange={(e) => setSkillInput(e.target.value)}
          className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
          placeholder="Type a skill and press Enter..."
        />
        <button
          type="submit"
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500"
        >
          Add
        </button>
      </form>

      {filteredSuggestions.length > 0 && skillInput.length > 0 && (
        <div className="mb-4">
          <p className="text-xs text-gray-500 mb-2">Suggestions:</p>
          <div className="flex flex-wrap gap-2">
            {filteredSuggestions.slice(0, 8).map((suggestion, index) => (
              <button
                key={index}
                onClick={() => handleSuggestionClick(suggestion)}
                className="px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded border hover:bg-gray-200 focus:outline-none"
              >
                {suggestion}
              </button>
            ))}
          </div>
        </div>
      )}

      <div className="mb-4">
        <p className="text-xs text-gray-500 mb-2">Popular skills:</p>
        <div className="flex flex-wrap gap-2">
          {suggestions.slice(0, 12).map((suggestion, index) => (
            <button
              key={index}
              onClick={() => handleSuggestionClick(suggestion)}
              className="px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded border border-blue-200 hover:bg-blue-100 focus:outline-none"
            >
              + {suggestion}
            </button>
          ))}
        </div>
      </div>
    </div>
  )
}

const CreateCV = () => {
  const [currentStep, setCurrentStep] = useState(0)
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [selectedColors, setSelectedColors] = useState(null)
  const [selectedColorName, setSelectedColorName] = useState(null)
  const [cvData, setCvData] = useState({
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      summary: '',
      profileImage: null
    },
    experience: [
      {
        id: 1,
        jobTitle: '',
        company: '',
        location: '',
        startDate: '',
        endDate: '',
        isCurrentJob: false,
        description: ''
      }
    ],
    skills: []
  })

  // Available templates with distinct layouts
  const templates = [
    {
      id: 'simple-one-column',
      name: 'Simple One Column',
      description: 'Clean single-column layout perfect for any profession',
      layout: 'simple-one-column',
      defaultColors: { primary: '#2563eb', secondary: '#64748b', accent: '#f8fafc' },
      colorOptions: [
        { name: 'Blue', colors: { primary: '#2563eb', secondary: '#64748b', accent: '#f8fafc' } },
        { name: 'Green', colors: { primary: '#059669', secondary: '#6b7280', accent: '#f0fdf4' } },
        { name: 'Purple', colors: { primary: '#7c3aed', secondary: '#a855f7', accent: '#faf5ff' } },
        { name: 'Red', colors: { primary: '#dc2626', secondary: '#6b7280', accent: '#fef2f2' } }
      ],
      features: ['ATS-Friendly', 'Single Column', 'Clean Design']
    },
    {
      id: 'professional-sidebar',
      name: 'Professional Sidebar',
      description: 'Two-column design with left sidebar for contact info',
      layout: 'professional-sidebar',
      defaultColors: { primary: '#1f2937', secondary: '#6b7280', accent: '#f3f4f6' },
      colorOptions: [
        { name: 'Dark', colors: { primary: '#1f2937', secondary: '#6b7280', accent: '#f3f4f6' } },
        { name: 'Navy', colors: { primary: '#1e40af', secondary: '#64748b', accent: '#eff6ff' } },
        { name: 'Teal', colors: { primary: '#0f766e', secondary: '#6b7280', accent: '#f0fdfa' } },
        { name: 'Indigo', colors: { primary: '#4338ca', secondary: '#6b7280', accent: '#eef2ff' } }
      ],
      features: ['Two Column', 'Sidebar Layout', 'Professional']
    },
    {
      id: 'modern-header',
      name: 'Modern Header',
      description: 'Bold header design with modern typography',
      layout: 'modern-header',
      defaultColors: { primary: '#7c3aed', secondary: '#a855f7', accent: '#faf5ff' },
      colorOptions: [
        { name: 'Purple', colors: { primary: '#7c3aed', secondary: '#a855f7', accent: '#faf5ff' } },
        { name: 'Orange', colors: { primary: '#ea580c', secondary: '#fb923c', accent: '#fff7ed' } },
        { name: 'Pink', colors: { primary: '#db2777', secondary: '#f472b6', accent: '#fdf2f8' } },
        { name: 'Emerald', colors: { primary: '#059669', secondary: '#34d399', accent: '#ecfdf5' } }
      ],
      features: ['Bold Header', 'Modern Design', 'Eye-catching']
    },
    {
      id: 'creative-blocks',
      name: 'Creative Blocks',
      description: 'Creative layout with colored blocks and sections',
      layout: 'creative-blocks',
      defaultColors: { primary: '#0891b2', secondary: '#0e7490', accent: '#ecfeff' },
      colorOptions: [
        { name: 'Cyan', colors: { primary: '#0891b2', secondary: '#0e7490', accent: '#ecfeff' } },
        { name: 'Violet', colors: { primary: '#8b5cf6', secondary: '#a78bfa', accent: '#f5f3ff' } },
        { name: 'Rose', colors: { primary: '#e11d48', secondary: '#fb7185', accent: '#fff1f2' } },
        { name: 'Amber', colors: { primary: '#d97706', secondary: '#fbbf24', accent: '#fffbeb' } }
      ],
      features: ['Creative Layout', 'Color Blocks', 'Designer-Focused']
    },
    {
      id: 'minimal-lines',
      name: 'Minimal Lines',
      description: 'Ultra-clean design with subtle line separators',
      layout: 'minimal-lines',
      defaultColors: { primary: '#374151', secondary: '#9ca3af', accent: '#ffffff' },
      colorOptions: [
        { name: 'Gray', colors: { primary: '#374151', secondary: '#9ca3af', accent: '#ffffff' } },
        { name: 'Slate', colors: { primary: '#475569', secondary: '#94a3b8', accent: '#f8fafc' } },
        { name: 'Stone', colors: { primary: '#57534e', secondary: '#a8a29e', accent: '#fafaf9' } },
        { name: 'Zinc', colors: { primary: '#52525b', secondary: '#a1a1aa', accent: '#fafafa' } }
      ],
      features: ['Minimal Design', 'Clean Lines', 'Content-Focused']
    },
    {
      id: 'executive-formal',
      name: 'Executive Formal',
      description: 'Traditional formal design for senior positions',
      layout: 'executive-formal',
      defaultColors: { primary: '#1f2937', secondary: '#6b7280', accent: '#f9fafb' },
      colorOptions: [
        { name: 'Classic', colors: { primary: '#1f2937', secondary: '#6b7280', accent: '#f9fafb' } },
        { name: 'Navy', colors: { primary: '#1e3a8a', secondary: '#64748b', accent: '#f1f5f9' } },
        { name: 'Forest', colors: { primary: '#14532d', secondary: '#6b7280', accent: '#f7fef7' } },
        { name: 'Burgundy', colors: { primary: '#7f1d1d', secondary: '#6b7280', accent: '#fef2f2' } }
      ],
      features: ['Executive Level', 'Formal Layout', 'Traditional']
    }
  ]

  const steps = ['Choose Template', 'Personal Info', 'Experience', 'Education', 'Skills', 'Review']

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template)
    setSelectedColors(template.defaultColors)
    setSelectedColorName(template.colorOptions[0].name) // Set first color as default
  }

  const handleColorChange = (colors, colorName) => {
    console.log('=== COLOR CHANGE TRIGGERED ===')
    console.log('New color name:', colorName)
    console.log('New colors:', colors)
    console.log('Previous colors:', selectedColors)

    setSelectedColors({ ...colors }) // Force new object reference
    setSelectedColorName(colorName)

    console.log('State should update now...')
  }

  const nextStep = () => {
    if (currentStep === 0 && !selectedTemplate) {
      alert('Please select a template first!')
      return
    }
    setCurrentStep(prev => Math.min(prev + 1, steps.length - 1))
  }

  const prevStep = () => {
    setCurrentStep(prev => Math.max(prev - 1, 0))
  }

  const updatePersonalInfo = (field, value) => {
    setCvData(prev => ({
      ...prev,
      personalInfo: { ...prev.personalInfo, [field]: value }
    }))
  }

  const handleImageUpload = (event) => {
    const file = event.target.files[0]
    if (file) {
      const reader = new FileReader()
      reader.onload = (e) => {
        updatePersonalInfo('profileImage', e.target.result)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = () => {
    updatePersonalInfo('profileImage', null)
  }

  const updateExperience = (id, field, value) => {
    setCvData(prev => ({
      ...prev,
      experience: prev.experience.map(exp =>
        exp.id === id ? { ...exp, [field]: value } : exp
      )
    }))
  }

  const addExperience = () => {
    const newId = Math.max(...cvData.experience.map(exp => exp.id)) + 1
    setCvData(prev => ({
      ...prev,
      experience: [...prev.experience, {
        id: newId,
        jobTitle: '',
        company: '',
        location: '',
        startDate: '',
        endDate: '',
        isCurrentJob: false,
        description: ''
      }]
    }))
  }

  const removeExperience = (id) => {
    if (cvData.experience.length > 1) {
      setCvData(prev => ({
        ...prev,
        experience: prev.experience.filter(exp => exp.id !== id)
      }))
    }
  }

  const addSkill = (skillName) => {
    if (skillName.trim() && !cvData.skills.includes(skillName.trim())) {
      setCvData(prev => ({
        ...prev,
        skills: [...prev.skills, skillName.trim()]
      }))
    }
  }

  const removeSkill = (skillToRemove) => {
    setCvData(prev => ({
      ...prev,
      skills: prev.skills.filter(skill => skill !== skillToRemove)
    }))
  }

  // Different layout renderers for CV preview
  const renderCVLayout = () => {
    if (!selectedTemplate || !selectedColors) return null

    const colors = selectedColors
    const { personalInfo } = cvData

    console.log('Rendering CV with colors:', colors)
    console.log('Template layout:', selectedTemplate.layout)

    switch (selectedTemplate.layout) {
      case 'simple-one-column':
        return (
          <div className="p-6 space-y-4" style={{ backgroundColor: colors.accent }}>
            {/* Debug color bar */}
            <div className="h-2 w-full rounded mb-4" style={{ backgroundColor: colors.primary }}></div>
            <div className="text-center pb-4 border-b-2" style={{ borderColor: colors.primary }}>
              {personalInfo.profileImage && (
                <img
                  src={personalInfo.profileImage}
                  alt="Profile"
                  className="w-24 h-24 rounded-full object-cover mx-auto mb-4 border-4"
                  style={{ borderColor: colors.primary }}
                />
              )}
              <h1 className="text-2xl font-bold mb-2" style={{ color: colors.primary }}>
                {personalInfo.fullName || 'Your Name'}
              </h1>
              <div className="text-sm space-y-1" style={{ color: colors.secondary }}>
                <p>{personalInfo.email || '<EMAIL>'}</p>
                <p>{personalInfo.phone || '+****************'}</p>
                <p>{personalInfo.location || 'City, Country'}</p>
              </div>
            </div>
            {personalInfo.summary && (
              <div className="p-4 rounded" style={{ backgroundColor: 'white' }}>
                <h2 className="text-lg font-semibold mb-2" style={{ color: colors.primary }}>Summary</h2>
                <p className="text-sm" style={{ color: colors.secondary }}>{personalInfo.summary}</p>
              </div>
            )}
            <div className="p-4 rounded" style={{ backgroundColor: 'white' }}>
              <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Experience</h2>
              <div className="space-y-3">
                {cvData.experience.filter(exp => exp.jobTitle || exp.company).length > 0 ? (
                  cvData.experience.filter(exp => exp.jobTitle || exp.company).map((exp, index) => (
                    <div key={exp.id} className="border-l-4 pl-3" style={{ borderColor: colors.primary }}>
                      <h3 className="font-semibold" style={{ color: colors.primary }}>
                        {exp.jobTitle || 'Job Title'}
                      </h3>
                      <p className="text-sm" style={{ color: colors.secondary }}>
                        {exp.company || 'Company'} {exp.location && `• ${exp.location}`}
                      </p>
                      {(exp.startDate || exp.endDate || exp.isCurrentJob) && (
                        <p className="text-xs" style={{ color: colors.secondary }}>
                          {exp.startDate && new Date(exp.startDate).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                          {exp.startDate && (exp.endDate || exp.isCurrentJob) && ' - '}
                          {exp.isCurrentJob ? 'Present' : exp.endDate && new Date(exp.endDate).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                        </p>
                      )}
                      {exp.description && (
                        <p className="text-sm mt-1" style={{ color: colors.secondary }}>
                          {exp.description}
                        </p>
                      )}
                    </div>
                  ))
                ) : (
                  <div className="border-l-4 pl-3" style={{ borderColor: colors.primary }}>
                    <h3 className="font-semibold" style={{ color: colors.primary }}>Senior Software Engineer</h3>
                    <p className="text-sm" style={{ color: colors.secondary }}>Tech Company • New York, NY</p>
                    <p className="text-xs" style={{ color: colors.secondary }}>Jan 2020 - Present</p>
                    <p className="text-sm mt-1" style={{ color: colors.secondary }}>Led development of key features and mentored junior developers.</p>
                  </div>
                )}
              </div>
            </div>
            <div className="p-4 rounded" style={{ backgroundColor: 'white' }}>
              <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Education</h2>
              <div className="border-l-4 pl-3" style={{ borderColor: colors.primary }}>
                <h3 className="font-semibold" style={{ color: colors.primary }}>Bachelor of Computer Science</h3>
                <p className="text-sm" style={{ color: colors.secondary }}>University Name • 2014 - 2018</p>
              </div>
            </div>
            <div className="p-4 rounded" style={{ backgroundColor: 'white' }}>
              <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Skills</h2>
              <div className="flex flex-wrap gap-2">
                {cvData.skills.length > 0 ? (
                  cvData.skills.map((skill, i) => (
                    <span key={i} className="px-2 py-1 text-xs rounded" style={{
                      backgroundColor: colors.primary,
                      color: 'white'
                    }}>
                      {skill}
                    </span>
                  ))
                ) : (
                  ['JavaScript', 'React', 'Node.js', 'Python', 'SQL', 'Git'].map((skill, i) => (
                    <span key={i} className="px-2 py-1 text-xs rounded" style={{
                      backgroundColor: colors.primary,
                      color: 'white'
                    }}>
                      {skill}
                    </span>
                  ))
                )}
              </div>
            </div>
          </div>
        )

      case 'professional-sidebar':
        return (
          <div className="flex h-full">
            <div className="w-1/3 p-4 space-y-4" style={{ backgroundColor: colors.accent }}>
              <div className="text-center">
                {personalInfo.profileImage ? (
                  <img
                    src={personalInfo.profileImage}
                    alt="Profile"
                    className="w-20 h-20 rounded-full object-cover mx-auto mb-3 border-2"
                    style={{ borderColor: colors.primary }}
                  />
                ) : (
                  <div className="w-20 h-20 rounded-full mx-auto mb-3" style={{ backgroundColor: colors.primary }}></div>
                )}
                <h1 className="text-lg font-bold" style={{ color: colors.primary }}>
                  {personalInfo.fullName || 'Your Name'}
                </h1>
              </div>
              <div>
                <h3 className="font-semibold text-sm mb-2" style={{ color: colors.primary }}>Contact</h3>
                <div className="text-xs space-y-1" style={{ color: colors.secondary }}>
                  <p>{personalInfo.email || '<EMAIL>'}</p>
                  <p>{personalInfo.phone || '+****************'}</p>
                  <p>{personalInfo.location || 'City, Country'}</p>
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-sm mb-2" style={{ color: colors.primary }}>Skills</h3>
                <div className="space-y-1">
                  {(cvData.skills.length > 0 ? cvData.skills : ['JavaScript', 'React', 'Node.js', 'Python', 'SQL', 'Git']).map((skill, i) => (
                    <div key={i} className="text-xs px-2 py-1 rounded mb-1" style={{
                      backgroundColor: colors.primary,
                      color: 'white'
                    }}>
                      {skill}
                    </div>
                  ))}
                </div>
              </div>
              <div>
                <h3 className="font-semibold text-sm mb-2" style={{ color: colors.primary }}>Education</h3>
                <div className="text-xs space-y-1" style={{ color: colors.secondary }}>
                  <p className="font-medium">Bachelor of CS</p>
                  <p>University • 2014-2018</p>
                </div>
              </div>
            </div>
            <div className="flex-1 p-4 space-y-4">
              {personalInfo.summary && (
                <div>
                  <h2 className="text-lg font-semibold mb-2" style={{ color: colors.primary }}>Summary</h2>
                  <p className="text-sm" style={{ color: colors.secondary }}>{personalInfo.summary}</p>
                </div>
              )}
              <div>
                <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Experience</h2>
                <div className="space-y-3">
                  {cvData.experience.filter(exp => exp.jobTitle || exp.company).length > 0 ? (
                    cvData.experience.filter(exp => exp.jobTitle || exp.company).map((exp) => (
                      <div key={exp.id} className="border-l-3 pl-3" style={{ borderColor: colors.primary }}>
                        <h3 className="font-semibold text-xs" style={{ color: colors.primary }}>
                          {exp.jobTitle || 'Job Title'}
                        </h3>
                        <p className="text-xs" style={{ color: colors.secondary }}>
                          {exp.company || 'Company'}
                        </p>
                        {(exp.startDate || exp.endDate || exp.isCurrentJob) && (
                          <p className="text-xs" style={{ color: colors.secondary }}>
                            {exp.startDate && new Date(exp.startDate).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                            {exp.startDate && (exp.endDate || exp.isCurrentJob) && ' - '}
                            {exp.isCurrentJob ? 'Present' : exp.endDate && new Date(exp.endDate).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                          </p>
                        )}
                        {exp.description && (
                          <p className="text-xs mt-1" style={{ color: colors.secondary }}>
                            {exp.description}
                          </p>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="border-l-3 pl-3" style={{ borderColor: colors.primary }}>
                      <h3 className="font-semibold text-xs" style={{ color: colors.primary }}>Senior Software Engineer</h3>
                      <p className="text-xs" style={{ color: colors.secondary }}>Tech Company</p>
                      <p className="text-xs" style={{ color: colors.secondary }}>2020 - Present</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )

      case 'modern-header':
        return (
          <div>
            <div className="p-6 text-white text-center" style={{ backgroundColor: colors.primary }}>
              {personalInfo.profileImage && (
                <img
                  src={personalInfo.profileImage}
                  alt="Profile"
                  className="w-20 h-20 rounded-full object-cover mx-auto mb-4 border-4 border-white"
                />
              )}
              <h1 className="text-2xl font-bold mb-2">{personalInfo.fullName || 'Your Name'}</h1>
              <p className="text-sm opacity-90">{personalInfo.email || '<EMAIL>'} • {personalInfo.phone || '+****************'}</p>
              {/* Debug: Show color value */}
              <p className="text-xs opacity-75 mt-2">Color: {colors.primary}</p>
            </div>
            <div className="p-6 space-y-4" style={{ backgroundColor: colors.accent }}>
              {personalInfo.summary && (
                <div>
                  <h2 className="text-lg font-semibold mb-2" style={{ color: colors.primary }}>Summary</h2>
                  <p className="text-sm" style={{ color: colors.secondary }}>{personalInfo.summary}</p>
                </div>
              )}
              <div>
                <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Experience</h2>
                <div className="space-y-3">
                  {cvData.experience.filter(exp => exp.jobTitle || exp.company).length > 0 ? (
                    cvData.experience.filter(exp => exp.jobTitle || exp.company).map((exp) => (
                      <div key={exp.id} className="p-3 rounded" style={{ backgroundColor: 'white', borderLeft: `4px solid ${colors.primary}` }}>
                        <h3 className="font-semibold" style={{ color: colors.primary }}>
                          {exp.jobTitle || 'Job Title'}
                        </h3>
                        <p className="text-sm" style={{ color: colors.secondary }}>
                          {exp.company || 'Company'} {exp.location && `• ${exp.location}`}
                        </p>
                        {(exp.startDate || exp.endDate || exp.isCurrentJob) && (
                          <p className="text-xs" style={{ color: colors.secondary }}>
                            {exp.startDate && new Date(exp.startDate).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                            {exp.startDate && (exp.endDate || exp.isCurrentJob) && ' - '}
                            {exp.isCurrentJob ? 'Present' : exp.endDate && new Date(exp.endDate).toLocaleDateString('en-US', { month: 'short', year: 'numeric' })}
                          </p>
                        )}
                        {exp.description && (
                          <p className="text-sm mt-1" style={{ color: colors.secondary }}>
                            {exp.description}
                          </p>
                        )}
                      </div>
                    ))
                  ) : (
                    <div className="p-3 rounded" style={{ backgroundColor: 'white', borderLeft: `4px solid ${colors.primary}` }}>
                      <h3 className="font-semibold" style={{ color: colors.primary }}>Senior Software Engineer</h3>
                      <p className="text-sm" style={{ color: colors.secondary }}>Tech Company • 2020 - Present</p>
                      <p className="text-sm mt-1" style={{ color: colors.secondary }}>Led development of key features and mentored junior developers.</p>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        )

      default:
        return (
          <div className="p-6 space-y-4">
            <div className="text-center border-b pb-4" style={{ borderColor: colors.secondary + '40' }}>
              {personalInfo.profileImage && (
                <img
                  src={personalInfo.profileImage}
                  alt="Profile"
                  className="w-20 h-20 rounded-full object-cover mx-auto mb-4 border-2"
                  style={{ borderColor: colors.primary }}
                />
              )}
              <h1 className="text-2xl font-bold mb-2" style={{ color: colors.primary }}>
                {personalInfo.fullName || 'Your Name'}
              </h1>
              <p className="text-sm" style={{ color: colors.secondary }}>
                {personalInfo.email || '<EMAIL>'} • {personalInfo.phone || '+****************'}
              </p>
            </div>
            <div>
              <h2 className="text-lg font-semibold mb-3" style={{ color: colors.primary }}>Experience</h2>
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold">Senior Software Engineer</h3>
                  <p className="text-sm" style={{ color: colors.secondary }}>Tech Company • 2020 - Present</p>
                </div>
              </div>
            </div>
          </div>
        )
    }
  }

  return (
    <div className="min-h-screen bg-gray-50 flex flex-col">
      {/* Header with Progress */}
      <div className="bg-white shadow-sm border-b flex-shrink-0">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-4">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold text-gray-900">Create Your CV</h1>
            <div className="flex items-center space-x-4">
              {steps.map((step, index) => (
                <div key={index} className="flex items-center">
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
                    index === currentStep ? 'bg-blue-600 text-white' :
                    index < currentStep ? 'bg-green-500 text-white' : 'bg-gray-300 text-gray-500'
                  }`}>
                    {index < currentStep ? '✓' : index + 1}
                  </div>
                  <span className={`ml-2 text-sm ${
                    index === currentStep ? 'text-blue-600 font-medium' : 'text-gray-500'
                  }`}>
                    {step}
                  </span>
                  {index < steps.length - 1 && <div className="w-8 h-0.5 bg-gray-300 ml-4"></div>}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content - Split Screen */}
      <div className="flex-1 flex overflow-hidden">
        {/* Left Panel - Form (50% width) */}
        <div className="w-1/2 overflow-y-auto border-r border-gray-200">
          <div className="py-8 px-6">
            {currentStep === 0 && (
              <div className="space-y-6">
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Choose Your Template</h2>
                  <p className="text-gray-600">Select a professional template to get started</p>
                </div>

                <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
                  {templates.map((template) => (
                    <div
                      key={template.id}
                      className={`cursor-pointer rounded-lg border-2 transition-all duration-200 ${
                        selectedTemplate?.id === template.id
                          ? 'border-blue-500 bg-blue-50 shadow-lg'
                          : 'border-gray-200 bg-white hover:border-gray-300 hover:shadow-md'
                      }`}
                      onClick={() => handleTemplateSelect(template)}
                    >
                      {/* Template Preview */}
                      <div className="p-4 relative">
                        <div className="h-40 bg-white rounded border overflow-hidden">
                          {template.layout === 'professional-sidebar' ? (
                            <div className="flex h-full">
                              <div className="w-1/3 p-2" style={{ backgroundColor: template.defaultColors.accent }}>
                                <div className="w-4 h-4 rounded-full mx-auto mb-1" style={{ backgroundColor: template.defaultColors.primary }}></div>
                                <div className="space-y-0.5">
                                  <div className="h-0.5 w-full rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                  <div className="h-0.5 w-3/4 rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                </div>
                              </div>
                              <div className="flex-1 p-2 space-y-1">
                                <div className="h-1 w-3/4 rounded" style={{ backgroundColor: template.defaultColors.primary }}></div>
                                <div className="h-0.5 w-full rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                <div className="h-0.5 w-5/6 rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                              </div>
                            </div>
                          ) : template.layout === 'modern-header' ? (
                            <div>
                              <div className="h-8 w-full flex items-center justify-center" style={{ backgroundColor: template.defaultColors.primary }}>
                                <div className="h-1 w-16 bg-white rounded"></div>
                              </div>
                              <div className="p-2 space-y-1">
                                <div className="h-0.5 w-full rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                <div className="h-0.5 w-4/5 rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                              </div>
                            </div>
                          ) : (
                            <div className="p-3 space-y-2">
                              <div className="h-2 rounded" style={{ backgroundColor: template.defaultColors.primary, width: '60%' }}></div>
                              <div className="h-1 rounded" style={{ backgroundColor: template.defaultColors.secondary, width: '40%' }}></div>
                              <div className="space-y-1 mt-3">
                                <div className="h-1 rounded" style={{ backgroundColor: template.defaultColors.secondary }}></div>
                                <div className="h-1 rounded" style={{ backgroundColor: template.defaultColors.secondary, width: '80%' }}></div>
                                <div className="h-1 rounded" style={{ backgroundColor: template.defaultColors.secondary, width: '60%' }}></div>
                              </div>
                            </div>
                          )}
                        </div>
                        {selectedTemplate?.id === template.id && (
                          <div className="absolute top-2 right-2 bg-blue-500 text-white rounded-full p-1">
                            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                            </svg>
                          </div>
                        )}
                      </div>

                      {/* Template Info */}
                      <div className="p-4 border-t">
                        <h3 className="font-semibold text-gray-900 mb-1">{template.name}</h3>
                        <p className="text-sm text-gray-600 mb-3">{template.description}</p>

                        {/* Color Options */}
                        {selectedTemplate?.id === template.id && (
                          <div className="mb-3">
                            <p className="text-xs font-medium text-gray-700 mb-2">Choose Color:</p>
                            <div className="flex gap-2">
                              {template.colorOptions.map((colorOption, index) => (
                                <button
                                  key={index}
                                  onClick={() => {
                                    console.log('Button clicked:', colorOption.name, colorOption.colors)
                                    handleColorChange(colorOption.colors, colorOption.name)
                                  }}
                                  className={`w-6 h-6 rounded-full border-2 transition-all ${
                                    selectedColorName === colorOption.name
                                      ? 'border-gray-800 scale-110 shadow-lg'
                                      : 'border-gray-300 hover:border-gray-500'
                                  }`}
                                  style={{ backgroundColor: colorOption.colors.primary }}
                                  title={colorOption.name}
                                />
                              ))}
                            </div>
                            <div className="mt-2 text-xs text-gray-500">
                              <p>Current: {selectedColorName || 'None'}</p>
                              <p>Primary: {selectedColors?.primary || 'None'}</p>
                            </div>
                            <div className="mt-2">
                              <p className="text-xs text-gray-500">Selected: {selectedColorName}</p>
                              <button
                                onClick={() => {
                                  console.log('=== FORCE COLOR TEST ===')
                                  const newColors = { primary: '#ff0000', secondary: '#666666', accent: '#ffeeee' }
                                  console.log('Setting colors to:', newColors)
                                  setSelectedColors(newColors)
                                  setSelectedColorName('Test Red')
                                  console.log('Force update complete')
                                }}
                                className="mt-1 px-2 py-1 text-xs bg-red-500 text-white rounded"
                              >
                                Force Red Test
                              </button>
                            </div>
                          </div>
                        )}

                        <div className="flex flex-wrap gap-1">
                          {template.features.map((feature, index) => (
                            <span key={index} className="px-2 py-1 text-xs rounded-full bg-gray-100 text-gray-600">
                              {feature}
                            </span>
                          ))}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {selectedTemplate && (
                  <div className="text-center">
                    <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-6 inline-block">
                      <p className="text-green-800">✓ Selected: <strong>{selectedTemplate.name}</strong></p>
                    </div>
                  </div>
                )}
              </div>
            )}
            
            {currentStep === 1 && (
              <div>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Personal Information</h2>
                  <p className="text-gray-600">Tell us about yourself and see the live preview update</p>
                </div>

                <div className="bg-white rounded-lg shadow p-6 space-y-6">
                  {/* Profile Image Upload */}
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-3">Profile Image (Optional)</label>
                    <div className="flex items-center space-x-4">
                      {cvData.personalInfo.profileImage ? (
                        <div className="relative">
                          <img
                            src={cvData.personalInfo.profileImage}
                            alt="Profile"
                            className="w-20 h-20 rounded-full object-cover border-2 border-gray-300"
                          />
                          <button
                            onClick={removeImage}
                            className="absolute -top-2 -right-2 bg-red-500 text-white rounded-full w-6 h-6 flex items-center justify-center text-xs hover:bg-red-600"
                          >
                            ×
                          </button>
                        </div>
                      ) : (
                        <div className="w-20 h-20 rounded-full bg-gray-100 border-2 border-dashed border-gray-300 flex items-center justify-center">
                          <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                          </svg>
                        </div>
                      )}
                      <div>
                        <input
                          type="file"
                          accept="image/*"
                          onChange={handleImageUpload}
                          className="hidden"
                          id="profile-image-upload"
                        />
                        <label
                          htmlFor="profile-image-upload"
                          className="cursor-pointer inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                        >
                          <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                          </svg>
                          Upload Photo
                        </label>
                        <p className="text-xs text-gray-500 mt-1">JPG, PNG up to 5MB</p>
                      </div>
                    </div>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                    <input
                      type="text"
                      value={cvData.personalInfo.fullName}
                      onChange={(e) => updatePersonalInfo('fullName', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Enter your full name"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                    <input
                      type="email"
                      value={cvData.personalInfo.email}
                      onChange={(e) => updatePersonalInfo('email', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="<EMAIL>"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                    <input
                      type="tel"
                      value={cvData.personalInfo.phone}
                      onChange={(e) => updatePersonalInfo('phone', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="+****************"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                    <input
                      type="text"
                      value={cvData.personalInfo.location}
                      onChange={(e) => updatePersonalInfo('location', e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="City, Country"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Professional Summary</label>
                    <textarea
                      value={cvData.personalInfo.summary}
                      onChange={(e) => updatePersonalInfo('summary', e.target.value)}
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                      placeholder="Brief summary of your professional background and goals..."
                    />
                  </div>
                </div>
              </div>
            )}

            {currentStep === 2 && (
              <div>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Work Experience</h2>
                  <p className="text-gray-600">Add your work experience and see it update in the preview</p>
                </div>

                <div className="space-y-6">
                  {cvData.experience.map((exp, index) => (
                    <div key={exp.id} className="bg-white rounded-lg shadow p-6">
                      <div className="flex items-center justify-between mb-4">
                        <h3 className="text-lg font-semibold text-gray-900">
                          Experience {index + 1}
                        </h3>
                        {cvData.experience.length > 1 && (
                          <button
                            onClick={() => removeExperience(exp.id)}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            Remove
                          </button>
                        )}
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Job Title</label>
                          <input
                            type="text"
                            value={exp.jobTitle}
                            onChange={(e) => updateExperience(exp.id, 'jobTitle', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="e.g. Senior Software Engineer"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Company</label>
                          <input
                            type="text"
                            value={exp.company}
                            onChange={(e) => updateExperience(exp.id, 'company', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="e.g. Tech Company Inc."
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Location</label>
                          <input
                            type="text"
                            value={exp.location}
                            onChange={(e) => updateExperience(exp.id, 'location', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                            placeholder="e.g. New York, NY"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">Start Date</label>
                          <input
                            type="month"
                            value={exp.startDate}
                            onChange={(e) => updateExperience(exp.id, 'startDate', e.target.value)}
                            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          />
                        </div>

                        <div>
                          <label className="block text-sm font-medium text-gray-700 mb-2">End Date</label>
                          <div className="space-y-2">
                            <input
                              type="month"
                              value={exp.endDate}
                              onChange={(e) => updateExperience(exp.id, 'endDate', e.target.value)}
                              disabled={exp.isCurrentJob}
                              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:bg-gray-100"
                            />
                            <label className="flex items-center">
                              <input
                                type="checkbox"
                                checked={exp.isCurrentJob}
                                onChange={(e) => {
                                  updateExperience(exp.id, 'isCurrentJob', e.target.checked)
                                  if (e.target.checked) {
                                    updateExperience(exp.id, 'endDate', '')
                                  }
                                }}
                                className="mr-2"
                              />
                              <span className="text-sm text-gray-600">I currently work here</span>
                            </label>
                          </div>
                        </div>
                      </div>

                      <div>
                        <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                        <textarea
                          value={exp.description}
                          onChange={(e) => updateExperience(exp.id, 'description', e.target.value)}
                          rows={4}
                          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                          placeholder="Describe your responsibilities and achievements..."
                        />
                      </div>
                    </div>
                  ))}

                  <button
                    onClick={addExperience}
                    className="w-full py-3 border-2 border-dashed border-gray-300 rounded-lg text-gray-600 hover:border-gray-400 hover:text-gray-700 transition-colors"
                  >
                    + Add Another Experience
                  </button>
                </div>
              </div>
            )}

            {currentStep === 4 && (
              <div>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">Skills</h2>
                  <p className="text-gray-600">Add your skills and see them appear as tags in your CV</p>
                </div>

                <div className="bg-white rounded-lg shadow p-6">
                  <div className="mb-6">
                    <label className="block text-sm font-medium text-gray-700 mb-3">Add Skills</label>
                    <SkillInput onAddSkill={addSkill} />
                  </div>

                  {cvData.skills.length > 0 && (
                    <div>
                      <h3 className="text-sm font-medium text-gray-700 mb-3">Your Skills</h3>
                      <div className="flex flex-wrap gap-2">
                        {cvData.skills.map((skill, index) => (
                          <div
                            key={index}
                            className="inline-flex items-center px-3 py-1 rounded-full text-sm bg-blue-100 text-blue-800 border border-blue-200"
                          >
                            <span>{skill}</span>
                            <button
                              onClick={() => removeSkill(skill)}
                              className="ml-2 text-blue-600 hover:text-blue-800 focus:outline-none"
                            >
                              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                              </svg>
                            </button>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {cvData.skills.length === 0 && (
                    <div className="text-center py-8 text-gray-500">
                      <svg className="w-12 h-12 mx-auto mb-4 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
                      </svg>
                      <p className="text-sm">Start adding your skills to see them in your CV</p>
                      <p className="text-xs text-gray-400 mt-1">Examples: JavaScript, React, Project Management, etc.</p>
                    </div>
                  )}

                  <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                    <h4 className="text-sm font-medium text-gray-700 mb-2">💡 Tips for adding skills:</h4>
                    <ul className="text-sm text-gray-600 space-y-1">
                      <li>• Add both technical and soft skills</li>
                      <li>• Use specific technologies (React, Python, etc.)</li>
                      <li>• Include relevant certifications</li>
                      <li>• Keep it relevant to your target job</li>
                    </ul>
                  </div>
                </div>
              </div>
            )}

            {currentStep > 4 && (
              <div>
                <div className="mb-6">
                  <h2 className="text-2xl font-bold text-gray-900 mb-2">{steps[currentStep]}</h2>
                  <p className="text-gray-600">This section will be implemented next. You can see your CV preview on the right.</p>
                </div>
                <div className="bg-white rounded-lg shadow p-6">
                  <div className="text-center py-12">
                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                      </svg>
                    </div>
                    <h3 className="text-lg font-semibold text-gray-900 mb-2">Coming Soon</h3>
                    <p className="text-gray-600 mb-4">
                      The <strong>{steps[currentStep]}</strong> section will be available in the next update.
                    </p>
                    <p className="text-sm text-gray-500">
                      For now, you can navigate between steps and see how your CV looks in the live preview.
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Panel - CV Preview (50% width) */}
        <div className="w-1/2 bg-gray-100 overflow-y-auto">
          <div className="p-6">
            <div className="mb-6">
              <h3 className="text-xl font-semibold text-gray-900 mb-3">Live Preview</h3>
              {selectedTemplate && (
                <div className="space-y-2">
                  <p className="text-sm text-gray-600">Template: {selectedTemplate.name}</p>
                  <div className="flex items-center gap-2">
                    <p className="text-xs text-gray-500">Color: {selectedColorName || 'Default'}</p>
                    {selectedColors && (
                      <div
                        className="w-4 h-4 rounded border border-gray-300"
                        style={{ backgroundColor: selectedColors.primary }}
                        title={`Primary: ${selectedColors.primary}`}
                      ></div>
                    )}
                  </div>
                </div>
              )}
            </div>
            <div className="flex justify-center">
              <div className="w-full max-w-md">
                <div className="bg-white shadow-xl rounded-lg overflow-hidden" style={{ aspectRatio: '8.5/11' }}>
                  <div className="text-sm h-full overflow-hidden" key={`${selectedTemplate?.id}-${selectedColorName}`}>
                    {selectedTemplate && selectedColors ? (
                      renderCVLayout()
                    ) : (
                      <div className="h-full flex items-center justify-center">
                        <div className="text-center text-gray-500 p-8">
                          <svg className="w-20 h-20 mx-auto mb-6 text-gray-300" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                          </svg>
                          <h4 className="text-lg font-medium text-gray-700 mb-2">CV Preview</h4>
                          <p className="text-sm">Select a template to see your CV preview</p>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Navigation */}
      <div className="bg-white border-t px-4 sm:px-6 lg:px-8 py-4 flex-shrink-0">
        <div className="max-w-7xl mx-auto flex justify-between">
          <button
            onClick={prevStep}
            disabled={currentStep === 0}
            className={`px-6 py-2 rounded-md font-medium ${
              currentStep === 0
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-gray-600 text-white hover:bg-gray-700'
            }`}
          >
            Previous
          </button>
          
          <button
            onClick={nextStep}
            className="px-6 py-2 bg-blue-600 text-white rounded-md font-medium hover:bg-blue-700"
          >
            {currentStep === steps.length - 1 ? 'Finish' : 'Next'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default CreateCV
