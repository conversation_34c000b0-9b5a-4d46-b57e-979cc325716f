import { useState } from 'react'

const AIEnhanceButton = ({ 
  text, 
  onEnhance, 
  type = 'summary', 
  disabled = false,
  className = '',
  size = 'sm'
}) => {
  const [isLoading, setIsLoading] = useState(false)

  const handleEnhance = async () => {
    if (!text || disabled) return

    setIsLoading(true)
    try {
      await onEnhance(text)
    } catch (error) {
      console.error('Enhancement failed:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const sizeClasses = {
    xs: 'px-2 py-1 text-xs',
    sm: 'px-3 py-1.5 text-sm',
    md: 'px-4 py-2 text-sm',
    lg: 'px-6 py-3 text-base'
  }

  const getButtonText = () => {
    if (isLoading) return 'Enhancing...'
    
    switch (type) {
      case 'summary': return '✨ Enhance Summary'
      case 'description': return '✨ Enhance Description'
      case 'skills': return '💡 Suggest Skills'
      case 'achievements': return '🎯 Generate Achievements'
      default: return '✨ AI Enhance'
    }
  }

  const getIcon = () => {
    if (isLoading) {
      return (
        <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      )
    }

    return (
      <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
      </svg>
    )
  }

  return (
    <button
      onClick={handleEnhance}
      disabled={disabled || isLoading || !text}
      className={`
        inline-flex items-center font-medium rounded-lg transition-all duration-200
        ${sizeClasses[size]}
        ${disabled || isLoading || !text
          ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
          : 'bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-md hover:shadow-lg transform hover:-translate-y-0.5'
        }
        ${className}
      `}
      title={!text ? 'Add some text to enhance' : 'Use AI to improve this content'}
    >
      {getIcon()}
      {getButtonText()}
    </button>
  )
}

export default AIEnhanceButton
