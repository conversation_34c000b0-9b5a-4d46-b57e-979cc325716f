import { useState, useEffect } from 'react'

const AIEnhancementModal = ({ 
  isOpen, 
  onClose, 
  originalText, 
  enhancedText, 
  onAccept, 
  onRegenerate,
  type = 'summary',
  isLoading = false 
}) => {
  const [selectedText, setSelectedText] = useState('')

  useEffect(() => {
    if (enhancedText) {
      setSelectedText(enhancedText)
    }
  }, [enhancedText])

  if (!isOpen) return null

  const handleAccept = () => {
    onAccept(selectedText)
    onClose()
  }

  const getTitle = () => {
    switch (type) {
      case 'summary': return 'AI-Enhanced Professional Summary'
      case 'description': return 'AI-Enhanced Job Description'
      case 'skills': return 'AI-Suggested Skills'
      case 'achievements': return 'AI-Generated Achievements'
      default: return 'AI Enhancement'
    }
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-hidden">
        {/* Header */}
        <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white p-6">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-bold">{getTitle()}</h2>
              <p className="text-purple-100 text-sm mt-1">
                Review and customize your AI-enhanced content
              </p>
            </div>
            <button
              onClick={onClose}
              className="text-white hover:text-gray-200 transition-colors"
            >
              <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="p-6 overflow-y-auto max-h-[calc(90vh-200px)]">
          {isLoading ? (
            <div className="flex items-center justify-center py-12">
              <div className="text-center">
                <svg className="animate-spin h-12 w-12 text-purple-600 mx-auto mb-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <h3 className="text-lg font-medium text-gray-900 mb-2">AI is working its magic...</h3>
                <p className="text-gray-600">This may take a few seconds</p>
              </div>
            </div>
          ) : (
            <div className="space-y-6">
              {/* Original vs Enhanced Comparison */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Original Text */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <span className="w-2 h-2 bg-gray-400 rounded-full mr-2"></span>
                    Original
                  </h3>
                  <div className="bg-gray-50 border border-gray-200 rounded-lg p-4">
                    <p className="text-gray-700 whitespace-pre-wrap">
                      {originalText || 'No original text provided'}
                    </p>
                  </div>
                </div>

                {/* Enhanced Text */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-3 flex items-center">
                    <span className="w-2 h-2 bg-purple-600 rounded-full mr-2"></span>
                    AI Enhanced
                  </h3>
                  <div className="bg-purple-50 border border-purple-200 rounded-lg p-4">
                    <textarea
                      value={selectedText}
                      onChange={(e) => setSelectedText(e.target.value)}
                      className="w-full h-32 bg-transparent border-none resize-none focus:outline-none text-gray-700"
                      placeholder="Enhanced content will appear here..."
                    />
                  </div>
                </div>
              </div>

              {/* Enhancement Features */}
              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-blue-900 mb-2">✨ AI Enhancements Applied:</h4>
                <ul className="text-sm text-blue-800 space-y-1">
                  <li>• Improved professional language and tone</li>
                  <li>• Added relevant industry keywords for ATS optimization</li>
                  <li>• Enhanced readability and impact</li>
                  <li>• Optimized length and structure</li>
                </ul>
              </div>

              {/* Tips */}
              <div className="bg-amber-50 border border-amber-200 rounded-lg p-4">
                <h4 className="text-sm font-medium text-amber-900 mb-2">💡 Pro Tips:</h4>
                <ul className="text-sm text-amber-800 space-y-1">
                  <li>• You can edit the enhanced text above before accepting</li>
                  <li>• Try regenerating for different variations</li>
                  <li>• Combine the best parts of original and enhanced versions</li>
                </ul>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        {!isLoading && (
          <div className="bg-gray-50 px-6 py-4 flex items-center justify-between">
            <div className="flex space-x-3">
              <button
                onClick={onRegenerate}
                className="px-4 py-2 text-sm font-medium text-purple-600 bg-white border border-purple-300 rounded-lg hover:bg-purple-50 transition-colors"
              >
                🔄 Regenerate
              </button>
              <button
                onClick={() => setSelectedText(originalText)}
                className="px-4 py-2 text-sm font-medium text-gray-600 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                ↶ Use Original
              </button>
            </div>
            
            <div className="flex space-x-3">
              <button
                onClick={onClose}
                className="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 transition-colors"
              >
                Cancel
              </button>
              <button
                onClick={handleAccept}
                disabled={!selectedText.trim()}
                className={`px-6 py-2 text-sm font-medium rounded-lg transition-colors ${
                  selectedText.trim()
                    ? 'bg-purple-600 text-white hover:bg-purple-700'
                    : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                }`}
              >
                Accept Enhancement
              </button>
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default AIEnhancementModal
