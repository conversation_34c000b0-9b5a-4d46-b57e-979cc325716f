import { useState } from 'react'

const SkillsStep = ({ cvData, updateCvData, nextStep, prevStep }) => {
  const [skills, setSkills] = useState(cvData.skills || [])
  const [currentSkill, setCurrentSkill] = useState('')
  const [skillCategory, setSkillCategory] = useState('Technical')
  const [proficiencyLevel, setProficiencyLevel] = useState('intermediate')

  const skillCategories = [
    'Technical',
    'Programming Languages',
    'Frameworks & Libraries',
    'Tools & Software',
    'Languages',
    'Soft Skills',
    'Other'
  ]

  const proficiencyLevels = [
    { value: 'beginner', label: 'Beginner' },
    { value: 'intermediate', label: 'Intermediate' },
    { value: 'advanced', label: 'Advanced' },
    { value: 'expert', label: 'Expert' }
  ]

  const addSkill = () => {
    if (!currentSkill.trim()) {
      alert('Please enter a skill')
      return
    }

    const newSkill = {
      name: currentSkill.trim(),
      category: skillCategory,
      proficiency: proficiencyLevel
    }

    const newSkills = [...skills, newSkill]
    setSkills(newSkills)
    updateCvData('skills', newSkills)
    setCurrentSkill('')
  }

  const deleteSkill = (index) => {
    const newSkills = skills.filter((_, i) => i !== index)
    setSkills(newSkills)
    updateCvData('skills', newSkills)
  }

  const handleKeyPress = (e) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      addSkill()
    }
  }

  // Group skills by category
  const groupedSkills = skills.reduce((acc, skill, index) => {
    const category = skill.category || 'Other'
    if (!acc[category]) {
      acc[category] = []
    }
    acc[category].push({ ...skill, originalIndex: index })
    return acc
  }, {})

  const getProficiencyColor = (proficiency) => {
    switch (proficiency) {
      case 'beginner': return 'bg-red-100 text-red-800'
      case 'intermediate': return 'bg-yellow-100 text-yellow-800'
      case 'advanced': return 'bg-blue-100 text-blue-800'
      case 'expert': return 'bg-green-100 text-green-800'
      default: return 'bg-gray-100 text-gray-800'
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Skills</h2>
        <p className="text-gray-600">Add your technical and soft skills</p>
      </div>

      {/* Add Skill Form */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Add New Skill</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div className="md:col-span-1">
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Skill Name *
            </label>
            <input
              type="text"
              value={currentSkill}
              onChange={(e) => setCurrentSkill(e.target.value)}
              onKeyPress={handleKeyPress}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g. JavaScript, Project Management"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Category
            </label>
            <select
              value={skillCategory}
              onChange={(e) => setSkillCategory(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              {skillCategories.map(category => (
                <option key={category} value={category}>{category}</option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Proficiency Level
            </label>
            <select
              value={proficiencyLevel}
              onChange={(e) => setProficiencyLevel(e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            >
              {proficiencyLevels.map(level => (
                <option key={level.value} value={level.value}>{level.label}</option>
              ))}
            </select>
          </div>
        </div>

        <button
          onClick={addSkill}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          Add Skill
        </button>
      </div>

      {/* Skills Display */}
      {skills.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Your Skills</h3>
          
          {Object.entries(groupedSkills).map(([category, categorySkills]) => (
            <div key={category} className="mb-6">
              <h4 className="text-md font-medium text-gray-800 mb-3 border-b border-gray-200 pb-1">
                {category}
              </h4>
              <div className="flex flex-wrap gap-2">
                {categorySkills.map((skill) => (
                  <div
                    key={skill.originalIndex}
                    className="inline-flex items-center bg-white border border-gray-200 rounded-lg px-3 py-2 shadow-sm"
                  >
                    <span className="text-sm font-medium text-gray-900 mr-2">
                      {skill.name}
                    </span>
                    <span className={`text-xs px-2 py-1 rounded-full ${getProficiencyColor(skill.proficiency)}`}>
                      {skill.proficiency}
                    </span>
                    <button
                      onClick={() => deleteSkill(skill.originalIndex)}
                      className="ml-2 text-red-500 hover:text-red-700 text-sm"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Skill Suggestions */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Skill Suggestions</h4>
        <p className="text-sm text-blue-800 mb-3">
          Consider adding these popular skills relevant to your field:
        </p>
        <div className="flex flex-wrap gap-2">
          {[
            'Communication', 'Leadership', 'Problem Solving', 'Teamwork',
            'Time Management', 'Critical Thinking', 'Adaptability', 'Creativity'
          ].map(suggestion => (
            <button
              key={suggestion}
              onClick={() => {
                setCurrentSkill(suggestion)
                setSkillCategory('Soft Skills')
              }}
              className="text-xs bg-blue-100 text-blue-800 px-2 py-1 rounded hover:bg-blue-200 transition-colors"
            >
              + {suggestion}
            </button>
          ))}
        </div>
      </div>

      {skills.length === 0 && (
        <div className="text-center py-8 text-gray-500">
          <svg className="mx-auto h-12 w-12 text-gray-400 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z" />
          </svg>
          <p className="text-lg font-medium text-gray-900 mb-2">No skills added yet</p>
          <p className="text-gray-600">Start by adding your most relevant skills above</p>
        </div>
      )}
    </div>
  )
}

export default SkillsStep
