import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import TemplateSelector from './TemplateSelector'
import PersonalInfoStep from './steps/PersonalInfoStep'
import ExperienceStep from './steps/ExperienceStep'
import EducationStep from './steps/EducationStep'
import SkillsStep from './steps/SkillsStep'
import ReviewStep from './steps/ReviewStep'
import CVPreview from './CVPreview'

const steps = [
  { id: 'template', name: 'Template', component: TemplateSelector },
  { id: 'personal', name: 'Personal Info', component: PersonalInfoStep },
  { id: 'experience', name: 'Experience', component: ExperienceStep },
  { id: 'education', name: 'Education', component: EducationStep },
  { id: 'skills', name: 'Skills', component: SkillsStep },
  { id: 'review', name: 'Review', component: ReviewStep }
]

const CVBuilderWizard = () => {
  const navigate = useNavigate()
  const { step } = useParams()
  const [currentStepIndex, setCurrentStepIndex] = useState(0)
  const [cvData, setCvData] = useState({
    template: null,
    personalInfo: {
      fullName: '',
      email: '',
      phone: '',
      location: '',
      linkedinUrl: '',
      summary: ''
    },
    experiences: [],
    educations: [],
    skills: [],
    certifications: [],
    languages: []
  })

  // Auto-save functionality
  useEffect(() => {
    const autoSave = () => {
      localStorage.setItem('cv-builder-draft', JSON.stringify(cvData))
    }

    const timer = setTimeout(autoSave, 2000) // Auto-save after 2 seconds of inactivity
    return () => clearTimeout(timer)
  }, [cvData])

  // Load draft on component mount
  useEffect(() => {
    const draft = localStorage.getItem('cv-builder-draft')
    if (draft) {
      try {
        setCvData(JSON.parse(draft))
      } catch (error) {
        console.error('Failed to load draft:', error)
      }
    }
  }, [])

  // Update step based on URL
  useEffect(() => {
    if (step) {
      const stepIndex = steps.findIndex(s => s.id === step)
      if (stepIndex !== -1) {
        setCurrentStepIndex(stepIndex)
      }
    }
  }, [step])

  const updateCvData = (section, data) => {
    setCvData(prev => ({
      ...prev,
      [section]: data
    }))
  }

  const nextStep = () => {
    if (currentStepIndex < steps.length - 1) {
      const nextIndex = currentStepIndex + 1
      setCurrentStepIndex(nextIndex)
      navigate(`/builder/${steps[nextIndex].id}`)
    }
  }

  const prevStep = () => {
    if (currentStepIndex > 0) {
      const prevIndex = currentStepIndex - 1
      setCurrentStepIndex(prevIndex)
      navigate(`/builder/${steps[prevIndex].id}`)
    }
  }

  const goToStep = (stepIndex) => {
    setCurrentStepIndex(stepIndex)
    navigate(`/builder/${steps[stepIndex].id}`)
  }

  const CurrentStepComponent = steps[currentStepIndex].component

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Progress Header */}
      <div className="bg-white shadow-sm border-b sticky top-0 z-10">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-xl font-bold text-gray-900">CV Builder</h1>
              <p className="text-sm text-gray-600">Step {currentStepIndex + 1} of {steps.length}</p>
            </div>
            
            {/* Progress Steps */}
            <div className="hidden md:flex items-center space-x-2">
              {steps.map((stepItem, index) => (
                <div key={stepItem.id} className="flex items-center">
                  <button
                    onClick={() => goToStep(index)}
                    className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors ${
                      index === currentStepIndex
                        ? 'bg-primary-600 text-white'
                        : index < currentStepIndex
                        ? 'bg-green-600 text-white'
                        : 'bg-gray-300 text-gray-500'
                    }`}
                  >
                    {index < currentStepIndex ? (
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    ) : (
                      index + 1
                    )}
                  </button>
                  <span className={`ml-2 text-sm ${
                    index === currentStepIndex ? 'text-primary-600 font-medium' : 'text-gray-500'
                  }`}>
                    {stepItem.name}
                  </span>
                  {index < steps.length - 1 && (
                    <div className="w-8 h-0.5 bg-gray-300 ml-2"></div>
                  )}
                </div>
              ))}
            </div>

            {/* Auto-save indicator */}
            <div className="flex items-center text-sm text-gray-500">
              <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
              </svg>
              Auto-saved
            </div>
          </div>
        </div>
      </div>

      <div className="flex">
        {/* Main Content */}
        <div className="flex-1">
          <CurrentStepComponent
            cvData={cvData}
            updateCvData={updateCvData}
            nextStep={nextStep}
            prevStep={prevStep}
            currentStep={currentStepIndex}
            totalSteps={steps.length}
          />
        </div>

        {/* Live Preview Sidebar */}
        {currentStepIndex > 0 && (
          <div className="hidden lg:block w-96 bg-white border-l border-gray-200">
            <div className="sticky top-20 p-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-4">Live Preview</h3>
              <div className="border border-gray-200 rounded-lg overflow-hidden">
                <CVPreview cvData={cvData} scale={0.6} />
              </div>
              <p className="text-xs text-gray-500 mt-2 text-center">
                Preview updates automatically as you type
              </p>
            </div>
          </div>
        )}
      </div>

      {/* Mobile Progress */}
      <div className="md:hidden fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
        <div className="flex justify-between items-center">
          <button
            onClick={prevStep}
            disabled={currentStepIndex === 0}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${
              currentStepIndex === 0
                ? 'text-gray-400 cursor-not-allowed'
                : 'text-gray-700 hover:bg-gray-100'
            }`}
          >
            Previous
          </button>
          
          <div className="flex space-x-1">
            {steps.map((_, index) => (
              <div
                key={index}
                className={`w-2 h-2 rounded-full ${
                  index === currentStepIndex
                    ? 'bg-primary-600'
                    : index < currentStepIndex
                    ? 'bg-green-600'
                    : 'bg-gray-300'
                }`}
              />
            ))}
          </div>

          <button
            onClick={nextStep}
            disabled={currentStepIndex === steps.length - 1}
            className={`px-4 py-2 rounded-lg text-sm font-medium ${
              currentStepIndex === steps.length - 1
                ? 'text-gray-400 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            Next
          </button>
        </div>
      </div>
    </div>
  )
}

export default CVBuilderWizard
