import { useState } from 'react'

const ExperienceStep = ({ cvData, updateCvData, nextStep, prevStep }) => {
  const [experiences, setExperiences] = useState(cvData.experience || [])
  const [currentExperience, setCurrentExperience] = useState({
    position: '',
    company: '',
    location: '',
    startDate: '',
    endDate: '',
    isCurrent: false,
    description: ''
  })
  const [isEditing, setIsEditing] = useState(false)
  const [editIndex, setEditIndex] = useState(-1)

  const handleInputChange = (field, value) => {
    setCurrentExperience(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addExperience = () => {
    if (!currentExperience.position || !currentExperience.company) {
      alert('Please fill in position and company fields')
      return
    }

    const newExperiences = isEditing 
      ? experiences.map((exp, index) => index === editIndex ? currentExperience : exp)
      : [...experiences, currentExperience]

    setExperiences(newExperiences)
    updateCvData('experience', newExperiences)
    
    // Reset form
    setCurrentExperience({
      position: '',
      company: '',
      location: '',
      startDate: '',
      endDate: '',
      isCurrent: false,
      description: ''
    })
    setIsEditing(false)
    setEditIndex(-1)
  }

  const editExperience = (index) => {
    setCurrentExperience(experiences[index])
    setIsEditing(true)
    setEditIndex(index)
  }

  const deleteExperience = (index) => {
    const newExperiences = experiences.filter((_, i) => i !== index)
    setExperiences(newExperiences)
    updateCvData('experience', newExperiences)
  }

  const handleCurrentJobChange = (checked) => {
    setCurrentExperience(prev => ({
      ...prev,
      isCurrent: checked,
      endDate: checked ? '' : prev.endDate
    }))
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Work Experience</h2>
        <p className="text-gray-600">Add your professional work experience</p>
      </div>

      {/* Experience Form */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {isEditing ? 'Edit Experience' : 'Add New Experience'}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Job Title *
            </label>
            <input
              type="text"
              value={currentExperience.position}
              onChange={(e) => handleInputChange('position', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g. Software Engineer"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Company *
            </label>
            <input
              type="text"
              value={currentExperience.company}
              onChange={(e) => handleInputChange('company', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g. Google Inc."
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Location
          </label>
          <input
            type="text"
            value={currentExperience.location}
            onChange={(e) => handleInputChange('location', e.target.value)}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="e.g. San Francisco, CA"
          />
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="month"
              value={currentExperience.startDate}
              onChange={(e) => handleInputChange('startDate', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="month"
              value={currentExperience.endDate}
              onChange={(e) => handleInputChange('endDate', e.target.value)}
              disabled={currentExperience.isCurrent}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100"
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={currentExperience.isCurrent}
              onChange={(e) => handleCurrentJobChange(e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="ml-2 text-sm text-gray-700">I currently work here</span>
          </label>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Job Description
          </label>
          <textarea
            value={currentExperience.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={4}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Describe your responsibilities and achievements..."
          />
        </div>

        <button
          onClick={addExperience}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          {isEditing ? 'Update Experience' : 'Add Experience'}
        </button>

        {isEditing && (
          <button
            onClick={() => {
              setIsEditing(false)
              setEditIndex(-1)
              setCurrentExperience({
                position: '',
                company: '',
                location: '',
                startDate: '',
                endDate: '',
                isCurrent: false,
                description: ''
              })
            }}
            className="ml-2 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-400 transition-colors"
          >
            Cancel
          </button>
        )}
      </div>

      {/* Experience List */}
      {experiences.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Added Experiences</h3>
          <div className="space-y-4">
            {experiences.map((exp, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{exp.position}</h4>
                    <p className="text-gray-600">{exp.company}</p>
                    {exp.location && <p className="text-sm text-gray-500">{exp.location}</p>}
                    <p className="text-sm text-gray-500">
                      {exp.startDate} - {exp.isCurrent ? 'Present' : exp.endDate || 'Present'}
                    </p>
                    {exp.description && (
                      <p className="text-sm text-gray-700 mt-2 line-clamp-3">{exp.description}</p>
                    )}
                  </div>
                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => editExperience(index)}
                      className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => deleteExperience(index)}
                      className="text-red-600 hover:text-red-700 text-sm font-medium"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default ExperienceStep
