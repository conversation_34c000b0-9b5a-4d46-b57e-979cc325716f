const CreativeDesignerTemplate = ({ cvData, colors }) => {
  const { personalInfo, experience, education, skills } = cvData

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden" style={{ aspectRatio: '8.5/11' }}>
      <div className="h-full flex">
        {/* Left Sidebar */}
        <div className="w-1/3 p-6" style={{ backgroundColor: colors.primary }}>
          <div className="text-white">
            {/* Profile */}
            <div className="text-center mb-6">
              <div className="w-24 h-24 bg-white rounded-full mx-auto mb-4 flex items-center justify-center">
                <svg className="w-12 h-12" style={{ color: colors.primary }} fill="currentColor" viewBox="0 0 24 24">
                  <path d="M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z"/>
                </svg>
              </div>
              <h1 className="text-xl font-bold mb-2">
                {personalInfo?.fullName || 'Your Name'}
              </h1>
            </div>

            {/* Contact Info */}
            <div className="mb-6">
              <h3 className="text-sm font-bold mb-3 uppercase tracking-wider">Contact</h3>
              <div className="space-y-2 text-sm">
                {personalInfo?.email && (
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                    </svg>
                    <span className="text-xs">{personalInfo.email}</span>
                  </div>
                )}
                {personalInfo?.phone && (
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                    </svg>
                    <span className="text-xs">{personalInfo.phone}</span>
                  </div>
                )}
                {personalInfo?.location && (
                  <div className="flex items-center">
                    <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                    </svg>
                    <span className="text-xs">{personalInfo.location}</span>
                  </div>
                )}
              </div>
            </div>

            {/* Skills */}
            {skills && skills.length > 0 && (
              <div className="mb-6">
                <h3 className="text-sm font-bold mb-3 uppercase tracking-wider">Skills</h3>
                <div className="space-y-3">
                  {Object.entries(
                    skills.reduce((acc, skill) => {
                      const category = skill.category || 'Other'
                      if (!acc[category]) acc[category] = []
                      acc[category].push(skill)
                      return acc
                    }, {})
                  ).map(([category, categorySkills]) => (
                    <div key={category}>
                      <h4 className="text-xs font-semibold mb-2 opacity-90">{category}</h4>
                      <div className="space-y-1">
                        {categorySkills.map((skill, index) => (
                          <div key={index} className="text-xs">
                            <div className="flex justify-between items-center mb-1">
                              <span>{skill.name}</span>
                              <span className="opacity-75">{skill.proficiency}</span>
                            </div>
                            <div className="w-full bg-white bg-opacity-30 rounded-full h-1">
                              <div 
                                className="bg-white h-1 rounded-full"
                                style={{ 
                                  width: skill.proficiency === 'expert' ? '100%' : 
                                         skill.proficiency === 'advanced' ? '80%' : 
                                         skill.proficiency === 'intermediate' ? '60%' : '40%'
                                }}
                              ></div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Right Content */}
        <div className="flex-1 p-6">
          {/* Professional Summary */}
          {personalInfo?.summary && (
            <div className="mb-6">
              <h2 className="text-lg font-bold mb-3" style={{ color: colors.primary }}>
                About Me
              </h2>
              <p className="text-gray-700 leading-relaxed text-sm">
                {personalInfo.summary}
              </p>
            </div>
          )}

          {/* Experience */}
          {experience && experience.length > 0 && (
            <div className="mb-6">
              <h2 className="text-lg font-bold mb-4" style={{ color: colors.primary }}>
                Experience
              </h2>
              <div className="space-y-4">
                {experience.map((exp, index) => (
                  <div key={index} className="relative">
                    <div className="flex justify-between items-start mb-2">
                      <div>
                        <h3 className="font-bold text-gray-900">{exp.position}</h3>
                        <p className="font-semibold" style={{ color: colors.secondary }}>{exp.company}</p>
                        {exp.location && <p className="text-sm text-gray-600">{exp.location}</p>}
                      </div>
                      <div className="text-right">
                        <span 
                          className="px-3 py-1 text-xs font-medium rounded-full text-white"
                          style={{ backgroundColor: colors.secondary }}
                        >
                          {exp.startDate} - {exp.isCurrent ? 'Present' : exp.endDate || 'Present'}
                        </span>
                      </div>
                    </div>
                    {exp.description && (
                      <p className="text-gray-700 text-sm leading-relaxed">{exp.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Education */}
          {education && education.length > 0 && (
            <div>
              <h2 className="text-lg font-bold mb-4" style={{ color: colors.primary }}>
                Education
              </h2>
              <div className="space-y-3">
                {education.map((edu, index) => (
                  <div key={index}>
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-bold text-gray-900">{edu.degree}</h3>
                        <p className="font-semibold" style={{ color: colors.secondary }}>{edu.institution}</p>
                        {edu.fieldOfStudy && <p className="text-sm text-gray-600">{edu.fieldOfStudy}</p>}
                        {edu.location && <p className="text-sm text-gray-600">{edu.location}</p>}
                      </div>
                      <div className="text-right">
                        <span 
                          className="px-3 py-1 text-xs font-medium rounded-full text-white"
                          style={{ backgroundColor: colors.secondary }}
                        >
                          {edu.startDate} - {edu.isCurrent ? 'Present' : edu.endDate || 'Present'}
                        </span>
                        {edu.gpa && <div className="text-xs text-gray-500 mt-1">GPA: {edu.gpa}</div>}
                      </div>
                    </div>
                    {edu.description && (
                      <p className="text-gray-700 text-sm mt-1">{edu.description}</p>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

export default CreativeDesignerTemplate
