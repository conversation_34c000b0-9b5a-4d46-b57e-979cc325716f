const ModernProfessionalTemplate = ({ cvData, colors }) => {
  const { personalInfo, experience, education, skills } = cvData

  return (
    <div className="bg-white shadow-lg rounded-lg overflow-hidden" style={{ aspectRatio: '8.5/11' }}>
      <div className="p-8 text-sm h-full">
        {/* Header */}
        <div className="text-center pb-6 mb-6 border-b-2" style={{ borderColor: colors.primary }}>
          <h1 className="text-3xl font-bold mb-2" style={{ color: colors.primary }}>
            {personalInfo?.fullName || 'Your Name'}
          </h1>
          <div className="text-gray-600 space-y-1">
            {personalInfo?.email && (
              <div className="flex items-center justify-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z" />
                </svg>
                {personalInfo.email}
              </div>
            )}
            {personalInfo?.phone && (
              <div className="flex items-center justify-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z" />
                </svg>
                {personalInfo.phone}
              </div>
            )}
            {personalInfo?.location && (
              <div className="flex items-center justify-center">
                <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                {personalInfo.location}
              </div>
            )}
          </div>
        </div>

        {/* Professional Summary */}
        {personalInfo?.summary && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-3 flex items-center" style={{ color: colors.primary }}>
              <div className="w-1 h-6 mr-3 rounded" style={{ backgroundColor: colors.primary }}></div>
              Professional Summary
            </h2>
            <p className="text-gray-700 leading-relaxed">
              {personalInfo.summary}
            </p>
          </div>
        )}

        {/* Experience Section */}
        {experience && experience.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-3 flex items-center" style={{ color: colors.primary }}>
              <div className="w-1 h-6 mr-3 rounded" style={{ backgroundColor: colors.primary }}></div>
              Work Experience
            </h2>
            <div className="space-y-4">
              {experience.map((exp, index) => (
                <div key={index} className="relative pl-4">
                  <div className="absolute left-0 top-1 w-2 h-2 rounded-full" style={{ backgroundColor: colors.primary }}></div>
                  <div className="flex justify-between items-start mb-1">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{exp.position}</h3>
                      <p className="font-medium" style={{ color: colors.primary }}>{exp.company}</p>
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      {exp.location && <div>{exp.location}</div>}
                      <div>
                        {exp.startDate} - {exp.isCurrent ? 'Present' : exp.endDate || 'Present'}
                      </div>
                    </div>
                  </div>
                  {exp.description && (
                    <p className="text-gray-700 text-sm leading-relaxed mt-2">{exp.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Education Section */}
        {education && education.length > 0 && (
          <div className="mb-6">
            <h2 className="text-lg font-semibold mb-3 flex items-center" style={{ color: colors.primary }}>
              <div className="w-1 h-6 mr-3 rounded" style={{ backgroundColor: colors.primary }}></div>
              Education
            </h2>
            <div className="space-y-3">
              {education.map((edu, index) => (
                <div key={index} className="relative pl-4">
                  <div className="absolute left-0 top-1 w-2 h-2 rounded-full" style={{ backgroundColor: colors.primary }}></div>
                  <div className="flex justify-between items-start">
                    <div className="flex-1">
                      <h3 className="font-semibold text-gray-900">{edu.degree}</h3>
                      <p className="font-medium" style={{ color: colors.primary }}>{edu.institution}</p>
                      {edu.fieldOfStudy && <p className="text-sm text-gray-600">{edu.fieldOfStudy}</p>}
                    </div>
                    <div className="text-right text-sm text-gray-500">
                      {edu.location && <div>{edu.location}</div>}
                      <div>
                        {edu.startDate} - {edu.isCurrent ? 'Present' : edu.endDate || 'Present'}
                      </div>
                      {edu.gpa && <div>GPA: {edu.gpa}</div>}
                    </div>
                  </div>
                  {edu.description && (
                    <p className="text-gray-700 text-sm mt-1">{edu.description}</p>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Skills Section */}
        {skills && skills.length > 0 && (
          <div>
            <h2 className="text-lg font-semibold mb-3 flex items-center" style={{ color: colors.primary }}>
              <div className="w-1 h-6 mr-3 rounded" style={{ backgroundColor: colors.primary }}></div>
              Skills
            </h2>
            <div className="space-y-3">
              {/* Group skills by category */}
              {Object.entries(
                skills.reduce((acc, skill) => {
                  const category = skill.category || 'Other'
                  if (!acc[category]) acc[category] = []
                  acc[category].push(skill)
                  return acc
                }, {})
              ).map(([category, categorySkills]) => (
                <div key={category}>
                  <h4 className="text-sm font-medium text-gray-800 mb-2">{category}</h4>
                  <div className="flex flex-wrap gap-2">
                    {categorySkills.map((skill, index) => (
                      <span 
                        key={index} 
                        className="px-3 py-1 text-xs font-medium rounded-full text-white"
                        style={{ backgroundColor: colors.primary }}
                      >
                        {skill.name}
                      </span>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}

export default ModernProfessionalTemplate
