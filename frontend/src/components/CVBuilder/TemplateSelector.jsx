import { useState } from 'react'
import { useNavigate } from 'react-router-dom'

const templates = [
  {
    id: 'riga',
    name: 'Riga',
    description: 'Professional and vibrant design that makes a lasting impression',
    image: '/templates/riga.jpg',
    category: 'modern',
    features: ['ATS-Friendly', 'Creative', 'Professional']
  },
  {
    id: 'sydney',
    name: 'Sydney',
    description: 'Clean and contemporary design perfect for any industry',
    image: '/templates/sydney.jpg',
    category: 'professional',
    features: ['ATS-Friendly', 'Clean', 'Versatile']
  },
  {
    id: 'chicago',
    name: 'Chicago',
    description: 'Classic professional template with clear structure',
    image: '/templates/chicago.jpg',
    category: 'classic',
    features: ['ATS-Friendly', 'Traditional', 'Corporate']
  },
  {
    id: 'budapest',
    name: 'Budapest',
    description: 'Modern layout with elegant typography',
    image: '/templates/budapest.jpg',
    category: 'modern',
    features: ['ATS-Friendly', 'Modern', 'Elegant']
  }
]

const TemplateSelector = ({ onTemplateSelect }) => {
  const [selectedTemplate, setSelectedTemplate] = useState(null)
  const [selectedCategory, setSelectedCategory] = useState('all')
  const navigate = useNavigate()

  const categories = [
    { id: 'all', name: 'All Templates' },
    { id: 'professional', name: 'Professional' },
    { id: 'modern', name: 'Modern' },
    { id: 'classic', name: 'Classic' },
    { id: 'creative', name: 'Creative' }
  ]

  const filteredTemplates = selectedCategory === 'all' 
    ? templates 
    : templates.filter(t => t.category === selectedCategory)

  const handleTemplateSelect = (template) => {
    setSelectedTemplate(template)
    onTemplateSelect(template)
  }

  const handleContinue = () => {
    if (selectedTemplate) {
      navigate('/builder/personal-info')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Choose Your Template</h1>
              <p className="text-gray-600 mt-1">Select a professional template to get started</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                <span className="text-sm font-medium text-primary-600">Template</span>
              </div>
              <div className="w-8 h-0.5 bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                <span className="text-sm text-gray-500">Personal Info</span>
              </div>
              <div className="w-8 h-0.5 bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                <span className="text-sm text-gray-500">Experience</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
          {filteredTemplates.map((template) => (
            <div
              key={template.id}
              className={`bg-white rounded-lg shadow-md overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg ${
                selectedTemplate?.id === template.id ? 'ring-2 ring-primary-600' : ''
              }`}
              onClick={() => handleTemplateSelect(template)}
            >
              <div className="aspect-[3/4] bg-gray-100 relative">
                {/* Template Preview Image */}
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="text-gray-400 text-center">
                    <svg className="w-16 h-16 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p className="text-sm">{template.name} Preview</p>
                  </div>
                </div>
                {selectedTemplate?.id === template.id && (
                  <div className="absolute top-2 right-2">
                    <div className="w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
              <div className="p-4">
                <h3 className="font-semibold text-gray-900 mb-1">{template.name}</h3>
                <p className="text-sm text-gray-600 mb-3">{template.description}</p>
                <div className="flex flex-wrap gap-1">
                  {template.features.map((feature) => (
                    <span
                      key={feature}
                      className="inline-block px-2 py-1 text-xs bg-gray-100 text-gray-700 rounded"
                    >
                      {feature}
                    </span>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Continue Button */}
        <div className="flex justify-center">
          <button
            onClick={handleContinue}
            disabled={!selectedTemplate}
            className={`px-8 py-3 rounded-lg font-medium transition-colors ${
              selectedTemplate
                ? 'bg-primary-600 text-white hover:bg-primary-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Continue with {selectedTemplate?.name || 'Selected Template'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default TemplateSelector
