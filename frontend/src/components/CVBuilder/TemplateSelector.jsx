import { useState } from 'react'
import { cvTemplates, templateCategories, getTemplatesByCategory } from '../../data/templates'

const TemplateSelector = ({ cvData, updateCvData, nextStep }) => {
  const [selectedTemplate, setSelectedTemplate] = useState(cvData.template || null)
  const [selectedCategory, setSelectedCategory] = useState('all')

  const filteredTemplates = getTemplatesByCategory(selectedCategory)

  // Debug logging
  console.log('TemplateSelector - selectedCategory:', selectedCategory)
  console.log('TemplateSelector - filteredTemplates:', filteredTemplates)
  console.log('TemplateSelector - selectedTemplate:', selectedTemplate)

  const handleTemplateSelect = (template) => {
    console.log('Template selected:', template)
    setSelectedTemplate(template)
    updateCvData('template', template)
    console.log('Template selection updated')
  }

  const handleContinue = () => {
    if (selectedTemplate) {
      nextStep()
    } else {
      alert('Please select a template to continue')
    }
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Choose Your Template</h1>
              <p className="text-gray-600 mt-1">Select a professional template to get started</p>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-primary-600 text-white rounded-full flex items-center justify-center text-sm font-medium">1</div>
                <span className="text-sm font-medium text-primary-600">Template</span>
              </div>
              <div className="w-8 h-0.5 bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">2</div>
                <span className="text-sm text-gray-500">Personal Info</span>
              </div>
              <div className="w-8 h-0.5 bg-gray-300"></div>
              <div className="flex items-center space-x-2">
                <div className="w-8 h-8 bg-gray-300 text-gray-500 rounded-full flex items-center justify-center text-sm font-medium">3</div>
                <span className="text-sm text-gray-500">Experience</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Category Filter */}
        <div className="mb-8">
          <div className="flex flex-wrap gap-2">
            {templateCategories.map((category) => (
              <button
                key={category.id}
                onClick={() => setSelectedCategory(category.id)}
                className={`px-4 py-2 rounded-full text-sm font-medium transition-colors ${
                  selectedCategory === category.id
                    ? 'bg-primary-600 text-white'
                    : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
                }`}
              >
                {category.name}
              </button>
            ))}
          </div>
        </div>

        {/* Debug Info */}
        <div className="mb-4 p-4 bg-yellow-100 border border-yellow-300 rounded">
          <p><strong>Debug:</strong> Found {filteredTemplates.length} templates</p>
          <p><strong>Selected:</strong> {selectedTemplate?.name || 'None'}</p>
          <button
            onClick={() => console.log('Current selection:', selectedTemplate)}
            className="mt-2 px-3 py-1 bg-blue-500 text-white rounded text-sm"
          >
            Log Selection
          </button>
        </div>

        {/* Templates Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
          {filteredTemplates.map((template) => (
            <div
              key={template.id}
              className={`rounded-lg shadow-md overflow-hidden cursor-pointer transition-all duration-200 hover:shadow-lg ${
                selectedTemplate?.id === template.id
                  ? 'ring-4 ring-blue-500 bg-blue-50'
                  : 'bg-white hover:bg-gray-50'
              }`}
              onClick={() => handleTemplateSelect(template)}
            >
              <div className="aspect-[3/4] relative overflow-hidden" style={{ backgroundColor: template.colors.accent }}>
                {/* Template Preview */}
                <div className="absolute inset-0 p-4 flex flex-col">
                  {/* Header */}
                  <div className="text-center mb-3">
                    <div
                      className="w-12 h-12 rounded-full mx-auto mb-2"
                      style={{ backgroundColor: template.colors.primary }}
                    ></div>
                    <div
                      className="h-2 w-16 mx-auto rounded mb-1"
                      style={{ backgroundColor: template.colors.primary }}
                    ></div>
                    <div
                      className="h-1 w-12 mx-auto rounded"
                      style={{ backgroundColor: template.colors.secondary }}
                    ></div>
                  </div>

                  {/* Content Lines */}
                  <div className="space-y-2 flex-1">
                    <div
                      className="h-1 w-full rounded"
                      style={{ backgroundColor: template.colors.secondary }}
                    ></div>
                    <div
                      className="h-1 w-3/4 rounded"
                      style={{ backgroundColor: template.colors.secondary }}
                    ></div>
                    <div
                      className="h-1 w-1/2 rounded"
                      style={{ backgroundColor: template.colors.secondary }}
                    ></div>
                  </div>
                </div>

                {/* Premium Badge */}
                {template.isPremium && (
                  <div className="absolute top-2 right-2 bg-yellow-400 text-yellow-900 text-xs font-bold px-2 py-1 rounded">
                    PRO
                  </div>
                )}
                {selectedTemplate?.id === template.id && (
                  <div className="absolute top-2 right-2">
                    <div className="w-6 h-6 bg-primary-600 text-white rounded-full flex items-center justify-center">
                      <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                  </div>
                )}
              </div>
              <div className="p-4">
                <div className="flex items-start justify-between mb-2">
                  <h3 className="font-semibold text-gray-900">{template.name}</h3>
                  <span className="text-xs bg-gray-100 text-gray-600 px-2 py-1 rounded">
                    {template.category}
                  </span>
                </div>
                <p className="text-sm text-gray-600 mb-3">{template.description}</p>

                {/* Features */}
                <div className="flex flex-wrap gap-1 mb-3">
                  {template.features.slice(0, 2).map((feature) => (
                    <span
                      key={feature}
                      className="inline-block px-2 py-1 text-xs bg-blue-50 text-blue-700 rounded"
                    >
                      {feature}
                    </span>
                  ))}
                </div>

                {/* Industries */}
                <div className="text-xs text-gray-500">
                  Best for: {template.industries.slice(0, 2).join(', ')}
                  {template.industries.length > 2 && ` +${template.industries.length - 2} more`}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Continue Button */}
        <div className="flex justify-center">
          <button
            onClick={handleContinue}
            disabled={!selectedTemplate}
            className={`px-8 py-3 rounded-lg font-medium transition-colors ${
              selectedTemplate
                ? 'bg-primary-600 text-white hover:bg-primary-700'
                : 'bg-gray-300 text-gray-500 cursor-not-allowed'
            }`}
          >
            Continue with {selectedTemplate?.name || 'Selected Template'}
          </button>
        </div>
      </div>
    </div>
  )
}

export default TemplateSelector
