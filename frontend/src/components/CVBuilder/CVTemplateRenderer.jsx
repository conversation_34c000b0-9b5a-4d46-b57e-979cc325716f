import ModernProfessionalTemplate from './templates/ModernProfessionalTemplate'
import CreativeDesignerTemplate from './templates/CreativeDesignerTemplate'

const CVTemplateRenderer = ({ cvData, template, scale = 1 }) => {
  if (!template) {
    return (
      <div className="bg-white shadow-lg rounded-lg overflow-hidden flex items-center justify-center" style={{ aspectRatio: '8.5/11' }}>
        <div className="text-center text-gray-400">
          <svg className="w-16 h-16 mx-auto mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
          </svg>
          <p className="text-lg font-medium">Select a Template</p>
          <p className="text-sm">Choose a template to see your CV preview</p>
        </div>
      </div>
    )
  }

  const renderTemplate = () => {
    const templateProps = {
      cvData,
      colors: template.colors
    }

    switch (template.id) {
      case 'modern-professional':
        return <ModernProfessionalTemplate {...templateProps} />
      case 'creative-designer':
        return <CreativeDesignerTemplate {...templateProps} />
      case 'executive-minimal':
        return <ModernProfessionalTemplate {...templateProps} />
      case 'tech-developer':
        return <ModernProfessionalTemplate {...templateProps} />
      case 'academic-researcher':
        return <ModernProfessionalTemplate {...templateProps} />
      case 'sales-marketing':
        return <CreativeDesignerTemplate {...templateProps} />
      default:
        return <ModernProfessionalTemplate {...templateProps} />
    }
  }

  return (
    <div 
      className="origin-top-left transition-transform duration-200"
      style={{ transform: `scale(${scale})` }}
    >
      {renderTemplate()}
    </div>
  )
}

export default CVTemplateRenderer
