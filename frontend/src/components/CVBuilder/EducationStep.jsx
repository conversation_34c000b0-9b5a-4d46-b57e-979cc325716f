import { useState } from 'react'

const EducationStep = ({ cvData, updateCvData, nextStep, prevStep }) => {
  const [educations, setEducations] = useState(cvData.education || [])
  const [currentEducation, setCurrentEducation] = useState({
    institution: '',
    degree: '',
    fieldOfStudy: '',
    location: '',
    startDate: '',
    endDate: '',
    isCurrent: false,
    gpa: '',
    description: ''
  })
  const [isEditing, setIsEditing] = useState(false)
  const [editIndex, setEditIndex] = useState(-1)

  const handleInputChange = (field, value) => {
    setCurrentEducation(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const addEducation = () => {
    if (!currentEducation.institution || !currentEducation.degree) {
      alert('Please fill in institution and degree fields')
      return
    }

    const newEducations = isEditing 
      ? educations.map((edu, index) => index === editIndex ? currentEducation : edu)
      : [...educations, currentEducation]

    setEducations(newEducations)
    updateCvData('education', newEducations)
    
    // Reset form
    setCurrentEducation({
      institution: '',
      degree: '',
      fieldOfStudy: '',
      location: '',
      startDate: '',
      endDate: '',
      isCurrent: false,
      gpa: '',
      description: ''
    })
    setIsEditing(false)
    setEditIndex(-1)
  }

  const editEducation = (index) => {
    setCurrentEducation(educations[index])
    setIsEditing(true)
    setEditIndex(index)
  }

  const deleteEducation = (index) => {
    const newEducations = educations.filter((_, i) => i !== index)
    setEducations(newEducations)
    updateCvData('education', newEducations)
  }

  const handleCurrentStudyChange = (checked) => {
    setCurrentEducation(prev => ({
      ...prev,
      isCurrent: checked,
      endDate: checked ? '' : prev.endDate
    }))
  }

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Education</h2>
        <p className="text-gray-600">Add your educational background</p>
      </div>

      {/* Education Form */}
      <div className="bg-gray-50 p-6 rounded-lg">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">
          {isEditing ? 'Edit Education' : 'Add New Education'}
        </h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Institution *
            </label>
            <input
              type="text"
              value={currentEducation.institution}
              onChange={(e) => handleInputChange('institution', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g. Harvard University"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Degree *
            </label>
            <input
              type="text"
              value={currentEducation.degree}
              onChange={(e) => handleInputChange('degree', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g. Bachelor of Science"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Field of Study
            </label>
            <input
              type="text"
              value={currentEducation.fieldOfStudy}
              onChange={(e) => handleInputChange('fieldOfStudy', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g. Computer Science"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Location
            </label>
            <input
              type="text"
              value={currentEducation.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g. Cambridge, MA"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Start Date
            </label>
            <input
              type="month"
              value={currentEducation.startDate}
              onChange={(e) => handleInputChange('startDate', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              End Date
            </label>
            <input
              type="month"
              value={currentEducation.endDate}
              onChange={(e) => handleInputChange('endDate', e.target.value)}
              disabled={currentEducation.isCurrent}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500 disabled:bg-gray-100"
            />
          </div>

          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              GPA (Optional)
            </label>
            <input
              type="text"
              value={currentEducation.gpa}
              onChange={(e) => handleInputChange('gpa', e.target.value)}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
              placeholder="e.g. 3.8/4.0"
            />
          </div>
        </div>

        <div className="mb-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={currentEducation.isCurrent}
              onChange={(e) => handleCurrentStudyChange(e.target.checked)}
              className="rounded border-gray-300 text-primary-600 focus:ring-primary-500"
            />
            <span className="ml-2 text-sm text-gray-700">I currently study here</span>
          </label>
        </div>

        <div className="mb-4">
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Description (Optional)
          </label>
          <textarea
            value={currentEducation.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            rows={3}
            className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
            placeholder="Relevant coursework, achievements, honors..."
          />
        </div>

        <button
          onClick={addEducation}
          className="bg-primary-600 text-white px-4 py-2 rounded-lg font-medium hover:bg-primary-700 transition-colors"
        >
          {isEditing ? 'Update Education' : 'Add Education'}
        </button>

        {isEditing && (
          <button
            onClick={() => {
              setIsEditing(false)
              setEditIndex(-1)
              setCurrentEducation({
                institution: '',
                degree: '',
                fieldOfStudy: '',
                location: '',
                startDate: '',
                endDate: '',
                isCurrent: false,
                gpa: '',
                description: ''
              })
            }}
            className="ml-2 bg-gray-300 text-gray-700 px-4 py-2 rounded-lg font-medium hover:bg-gray-400 transition-colors"
          >
            Cancel
          </button>
        )}
      </div>

      {/* Education List */}
      {educations.length > 0 && (
        <div>
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Added Education</h3>
          <div className="space-y-4">
            {educations.map((edu, index) => (
              <div key={index} className="bg-white border border-gray-200 rounded-lg p-4">
                <div className="flex justify-between items-start">
                  <div className="flex-1">
                    <h4 className="font-semibold text-gray-900">{edu.degree}</h4>
                    <p className="text-gray-600">{edu.institution}</p>
                    {edu.fieldOfStudy && <p className="text-sm text-gray-500">{edu.fieldOfStudy}</p>}
                    {edu.location && <p className="text-sm text-gray-500">{edu.location}</p>}
                    <p className="text-sm text-gray-500">
                      {edu.startDate} - {edu.isCurrent ? 'Present' : edu.endDate || 'Present'}
                    </p>
                    {edu.gpa && <p className="text-sm text-gray-500">GPA: {edu.gpa}</p>}
                    {edu.description && (
                      <p className="text-sm text-gray-700 mt-2 line-clamp-2">{edu.description}</p>
                    )}
                  </div>
                  <div className="flex space-x-2 ml-4">
                    <button
                      onClick={() => editEducation(index)}
                      className="text-primary-600 hover:text-primary-700 text-sm font-medium"
                    >
                      Edit
                    </button>
                    <button
                      onClick={() => deleteEducation(index)}
                      className="text-red-600 hover:text-red-700 text-sm font-medium"
                    >
                      Delete
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  )
}

export default EducationStep
