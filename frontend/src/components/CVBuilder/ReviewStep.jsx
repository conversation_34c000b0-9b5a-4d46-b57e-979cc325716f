import { useState } from 'react'
import { useAuth } from '../../contexts/AuthContext'
import { useNavigate } from 'react-router-dom'

const ReviewStep = ({ cvData, updateCvData, prevStep }) => {
  const { user } = useAuth()
  const navigate = useNavigate()
  const [isGeneratingPDF, setIsGeneratingPDF] = useState(false)
  const [cvTitle, setCvTitle] = useState('My Professional CV')

  const handleDownloadPDF = async () => {
    if (!user) {
      // Store CV data in localStorage for after login
      localStorage.setItem('pendingCVData', JSON.stringify({ ...cvData, title: cvTitle }))
      localStorage.setItem('pendingAction', 'download')
      navigate('/login?redirect=create-cv&action=download')
      return
    }

    setIsGeneratingPDF(true)
    try {
      // TODO: Implement PDF generation
      await new Promise(resolve => setTimeout(resolve, 2000)) // Simulate PDF generation
      alert('PDF generation will be implemented in the next phase!')
    } catch (error) {
      alert('Error generating PDF: ' + error.message)
    } finally {
      setIsGeneratingPDF(false)
    }
  }

  const handleSaveCV = async () => {
    if (!user) {
      // Store CV data in localStorage for after login
      localStorage.setItem('pendingCVData', JSON.stringify({ ...cvData, title: cvTitle }))
      localStorage.setItem('pendingAction', 'save')
      navigate('/login?redirect=create-cv&action=save')
      return
    }

    try {
      // TODO: Implement CV saving to backend
      const cvPayload = {
        title: cvTitle,
        personalInfo: cvData.personalInfo,
        experience: cvData.experience || [],
        education: cvData.education || [],
        skills: cvData.skills || []
      }

      console.log('CV Data to save:', cvPayload)
      alert('CV saving will be implemented in the next phase!')
    } catch (error) {
      alert('Error saving CV: ' + error.message)
    }
  }

  const getCompletionPercentage = () => {
    let completed = 0
    let total = 5

    if (cvData.personalInfo?.fullName && cvData.personalInfo?.email) completed++
    if (cvData.personalInfo?.summary) completed++
    if (cvData.experience?.length > 0) completed++
    if (cvData.education?.length > 0) completed++
    if (cvData.skills?.length > 0) completed++

    return Math.round((completed / total) * 100)
  }

  const completionPercentage = getCompletionPercentage()

  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-2xl font-bold text-gray-900 mb-2">Review Your CV</h2>
        <p className="text-gray-600">Review your information and download your professional CV</p>
      </div>

      {/* Completion Status */}
      <div className="bg-gradient-to-r from-primary-50 to-blue-50 p-6 rounded-lg">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold text-gray-900">CV Completion</h3>
          <span className="text-2xl font-bold text-primary-600">{completionPercentage}%</span>
        </div>
        <div className="w-full bg-gray-200 rounded-full h-3 mb-4">
          <div 
            className="bg-primary-600 h-3 rounded-full transition-all duration-500"
            style={{ width: `${completionPercentage}%` }}
          ></div>
        </div>
        <p className="text-sm text-gray-600">
          {completionPercentage === 100 
            ? "🎉 Your CV is complete and ready to download!"
            : "Add more information to improve your CV completeness"
          }
        </p>
      </div>

      {/* CV Title */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">CV Title</h3>
        <input
          type="text"
          value={cvTitle}
          onChange={(e) => setCvTitle(e.target.value)}
          className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-primary-500"
          placeholder="Enter a title for your CV"
        />
      </div>

      {/* CV Summary */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">CV Summary</h3>
        
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Personal Information */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2 flex items-center">
              <span className="w-2 h-2 bg-primary-600 rounded-full mr-2"></span>
              Personal Information
            </h4>
            <div className="text-sm text-gray-600 space-y-1">
              <p><strong>Name:</strong> {cvData.personalInfo?.fullName || 'Not provided'}</p>
              <p><strong>Email:</strong> {cvData.personalInfo?.email || 'Not provided'}</p>
              <p><strong>Phone:</strong> {cvData.personalInfo?.phone || 'Not provided'}</p>
              <p><strong>Location:</strong> {cvData.personalInfo?.location || 'Not provided'}</p>
            </div>
          </div>

          {/* Professional Summary */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2 flex items-center">
              <span className="w-2 h-2 bg-green-600 rounded-full mr-2"></span>
              Professional Summary
            </h4>
            <div className="text-sm text-gray-600">
              {cvData.personalInfo?.summary ? (
                <p className="line-clamp-4">{cvData.personalInfo.summary}</p>
              ) : (
                <p className="italic">No summary provided</p>
              )}
            </div>
          </div>

          {/* Experience */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2 flex items-center">
              <span className="w-2 h-2 bg-blue-600 rounded-full mr-2"></span>
              Work Experience
            </h4>
            <div className="text-sm text-gray-600">
              {cvData.experience?.length > 0 ? (
                <p>{cvData.experience.length} experience(s) added</p>
              ) : (
                <p className="italic">No experience added</p>
              )}
            </div>
          </div>

          {/* Education */}
          <div>
            <h4 className="font-medium text-gray-900 mb-2 flex items-center">
              <span className="w-2 h-2 bg-purple-600 rounded-full mr-2"></span>
              Education
            </h4>
            <div className="text-sm text-gray-600">
              {cvData.education?.length > 0 ? (
                <p>{cvData.education.length} education(s) added</p>
              ) : (
                <p className="italic">No education added</p>
              )}
            </div>
          </div>

          {/* Skills */}
          <div className="md:col-span-2">
            <h4 className="font-medium text-gray-900 mb-2 flex items-center">
              <span className="w-2 h-2 bg-yellow-600 rounded-full mr-2"></span>
              Skills
            </h4>
            <div className="text-sm text-gray-600">
              {cvData.skills?.length > 0 ? (
                <div className="flex flex-wrap gap-1">
                  {cvData.skills.slice(0, 10).map((skill, index) => (
                    <span key={index} className="bg-gray-100 text-gray-700 px-2 py-1 rounded text-xs">
                      {skill.name}
                    </span>
                  ))}
                  {cvData.skills.length > 10 && (
                    <span className="text-gray-500 text-xs">+{cvData.skills.length - 10} more</span>
                  )}
                </div>
              ) : (
                <p className="italic">No skills added</p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="bg-white border border-gray-200 rounded-lg p-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-4">Download & Save</h3>
        
        <div className="flex flex-col sm:flex-row gap-4">
          <button
            onClick={handleDownloadPDF}
            disabled={isGeneratingPDF || completionPercentage < 60}
            className={`flex-1 flex items-center justify-center px-6 py-3 rounded-lg font-medium transition-colors ${
              isGeneratingPDF || completionPercentage < 60
                ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
                : 'bg-primary-600 text-white hover:bg-primary-700'
            }`}
          >
            {isGeneratingPDF ? (
              <>
                <svg className="animate-spin -ml-1 mr-3 h-5 w-5 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Generating PDF...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
                Download PDF
              </>
            )}
          </button>

          <button
            onClick={handleSaveCV}
            className="flex-1 flex items-center justify-center px-6 py-3 border border-gray-300 rounded-lg font-medium text-gray-700 hover:bg-gray-50 transition-colors"
          >
            <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3-3m0 0l-3 3m3-3v12" />
            </svg>
            Save CV
          </button>
        </div>

        {!user && (
          <div className="mt-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
            <p className="text-sm text-blue-700 flex items-center">
              <svg className="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              You'll be asked to create a free account to save or download your CV
            </p>
          </div>
        )}

        {completionPercentage < 60 && (
          <p className="text-sm text-amber-600 mt-2 flex items-center">
            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
            </svg>
            Complete at least 60% of your CV to download PDF
          </p>
        )}
      </div>

      {/* Tips */}
      <div className="bg-blue-50 p-4 rounded-lg">
        <h4 className="text-sm font-medium text-blue-900 mb-2">💡 Pro Tips</h4>
        <ul className="text-sm text-blue-800 space-y-1">
          <li>• Keep your CV to 1-2 pages for most positions</li>
          <li>• Use action verbs to describe your achievements</li>
          <li>• Tailor your CV for each job application</li>
          <li>• Proofread carefully for spelling and grammar errors</li>
        </ul>
      </div>
    </div>
  )
}

export default ReviewStep
