// API Configuration
export const API_BASE_URL = import.meta.env.VITE_API_URL || 'http://localhost:3001/api'

// API endpoints
export const API_ENDPOINTS = {
  auth: {
    login: '/auth/login',
    register: '/auth/register',
    logout: '/auth/logout',
    refresh: '/auth/refresh',
    me: '/auth/me'
  },
  resumes: {
    list: '/resumes',
    create: '/resumes',
    get: (id) => `/resumes/${id}`,
    update: (id) => `/resumes/${id}`,
    delete: (id) => `/resumes/${id}`,
    export: (id, format) => `/resumes/${id}/export/${format}`
  },
  ai: {
    enhanceSummary: '/ai/enhance-summary',
    enhanceJobDescription: '/ai/enhance-job-description',
    suggestSkills: '/ai/suggest-skills',
    optimizeATS: '/ai/optimize-ats',
    generateAchievements: '/ai/generate-achievements',
    generateCoverLetter: '/ai/generate-cover-letter',
    generateInterviewQuestions: '/ai/generate-interview-questions',
    usageStats: '/ai/usage-stats'
  }
}

// Helper function to build full URL
export const buildApiUrl = (endpoint) => {
  return `${API_BASE_URL}${endpoint}`
}
