import { createContext, useContext, useState, useEffect } from 'react'
import axios from 'axios'

const AuthContext = createContext()

export const useAuth = () => {
  const context = useContext(AuthContext)
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider')
  }
  return context
}

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null)
  const [loading, setLoading] = useState(true)
  const [accessToken, setAccessToken] = useState(localStorage.getItem('accessToken'))
  const [refreshToken, setRefreshToken] = useState(localStorage.getItem('refreshToken'))

  // Configure axios defaults
  useEffect(() => {
    if (accessToken) {
      axios.defaults.headers.common['Authorization'] = `Bearer ${accessToken}`
    } else {
      delete axios.defaults.headers.common['Authorization']
    }
  }, [accessToken])

  // Check if user is logged in on app start
  useEffect(() => {
    const checkAuth = async () => {
      if (accessToken) {
        try {
          const response = await axios.get('http://localhost:3001/api/auth/me')
          setUser(response.data.user)
        } catch (error) {
          // Try to refresh token
          if (refreshToken && error.response?.status === 403) {
            const refreshResult = await refreshAccessToken()
            if (!refreshResult.success) {
              clearTokens()
            }
          } else {
            clearTokens()
          }
        }
      }
      setLoading(false)
    }
    checkAuth()
  }, [accessToken, refreshToken])

  const clearTokens = () => {
    localStorage.removeItem('accessToken')
    localStorage.removeItem('refreshToken')
    setAccessToken(null)
    setRefreshToken(null)
    setUser(null)
  }

  const refreshAccessToken = async () => {
    try {
      const response = await axios.post('http://localhost:3001/api/auth/refresh', {
        refreshToken
      })
      const { accessToken: newAccessToken } = response.data
      localStorage.setItem('accessToken', newAccessToken)
      setAccessToken(newAccessToken)
      return { success: true }
    } catch (error) {
      return { success: false }
    }
  }

  const login = async (email, password) => {
    try {
      const response = await axios.post('http://localhost:3001/api/auth/login', {
        email,
        password
      })
      const { user, accessToken, refreshToken } = response.data

      localStorage.setItem('accessToken', accessToken)
      localStorage.setItem('refreshToken', refreshToken)
      setAccessToken(accessToken)
      setRefreshToken(refreshToken)
      setUser(user)

      return { success: true }
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.error || 'Login failed'
      }
    }
  }

  const register = async (full_name, email, password, phone = '', locale = 'en') => {
    try {
      const response = await axios.post('http://localhost:3001/api/auth/register', {
        full_name,
        email,
        password,
        phone,
        locale
      })
      // Auto login after registration
      const loginResult = await login(email, password)
      return loginResult
    } catch (error) {
      return {
        success: false,
        error: error.response?.data?.error || 'Registration failed'
      }
    }
  }

  const logout = async () => {
    try {
      if (accessToken) {
        await axios.post('http://localhost:3001/api/auth/logout')
      }
    } catch (error) {
      // Continue with logout even if server call fails
    }
    clearTokens()
  }

  const value = {
    user,
    login,
    register,
    logout,
    loading,
    refreshAccessToken
  }

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  )
}
